-- 学生预警系统数据库初始化脚本
-- 用于创建测试数据

-- 1. 创建测试学生基本信息
INSERT INTO T_GXXS_BKSJBXX (WYBS, XH, XM, XBM, TTIMESTAMP) VALUES
('001', '202310001', '张三', '1', '20241220120000'),
('002', '202310002', '李四', '2', '20241220120000'),
('003', '202310003', '王五', '1', '20241220120000'),
('004', '202310004', '赵六', '2', '20241220120000'),
('005', '202310005', '钱七', '1', '20241220120000');

-- 2. 创建测试学籍信息
INSERT INTO T_GXXS_BKSXJJBXX (WYBS, XH, XDYXBM, ZYBM, ZYBBM, NJ, TTIMESTAMP) VALUES
('001', '202310001', 'FD000020', '00001', 'CS2023001', '2023级', '20241220120000'),
('002', '202310002', 'FD000020', '00001', 'CS2023001', '2023级', '20241220120000'),
('003', '202310003', 'FD000021', '00002', 'EE2023001', '2023级', '20241220120000'),
('004', '202310004', 'FD000021', '00002', 'EE2023001', '2023级', '20241220120000'),
('005', '202310005', 'FD000020', '00001', 'CS2023002', '2023级', '20241220120000');

-- 3. 创建组织机构测试数据
INSERT INTO DM_XB_ZZJGM (ZZJGM, ZZJGMC, SFQY, SJCS) VALUES
('FD000020', '计算机科学与技术学院', 1, '20241220120000'),
('FD000021', '电子工程与信息科学系', 1, '20241220120000'),
('FD000022', '数学科学学院', 1, '20241220120000');

-- 4. 创建专业测试数据
INSERT INTO DM_XB_BKSZYM (ZYBM, ZYMC, SFQY, SJCS) VALUES
('00001', '计算机科学与技术', 1, '20241220120000'),
('00002', '电子信息工程', 1, '20241220120000'),
('00003', '数学与应用数学', 1, '20241220120000');

-- 5. 创建专业班级测试数据
INSERT INTO DM_XB_BKSZYBJM (ZYBBM, ZYBBMC, NJ, SFQY, SJCS) VALUES
('CS2023001', '计算机科学与技术1班', '2023', 1, '20241220120000'),
('CS2023002', '计算机科学与技术2班', '2023', 1, '20241220120000'),
('EE2023001', '电子信息工程1班', '2023', 1, '20241220120000');

-- 6. 创建预警信息测试数据
INSERT INTO T_GXYS_YJXX (WYBS, XH, YJLX_M, YJDJ_M, YJZT_M, YJMS, CJSJ, TTIMESTAMP) VALUES
-- 张三：学业预警(高风险) + 轨迹预警(中风险)
('W001', '202310001', '01', '3', '01', '学业成绩严重下滑，多门课程不及格', '20241220120000', '20241220120000'),
('W002', '202310001', '11', '2', '01', '经常缺课，行为轨迹异常', '20241220120000', '20241220120000'),

-- 李四：生活预警(低风险)
('W003', '202310002', '21', '1', '01', '生活作息不规律', '20241220120000', '20241220120000'),

-- 王五：心理预警(高风险)
('W004', '202310003', '31', '3', '01', '心理状态异常，需要重点关注', '20241220120000', '20241220120000'),

-- 赵六：学业预警(中风险) - 已解决
('W005', '202310004', '02', '2', '03', '期中考试成绩不理想，已通过辅导改善', '20241220120000', '20241220120000'),

-- 钱七：无预警记录（用于测试NONE状态）
('W006', '202310006', '01', '1', '04', '历史预警记录，已忽略', '20241220120000', '20241220120000');


-- ----------------------------
-- Table structure for T_GXTS_JYLSXX
-- ----------------------------
DROP TABLE IF EXISTS `T_GXTS_JYLSXX`;
CREATE TABLE `T_GXTS_JYLSXX` (
                                 `GID` VARCHAR(100) NOT NULL COMMENT 'GID',
                                 `XGH` VARCHAR(100) DEFAULT NULL COMMENT '学工号',
                                 `JYSJ` VARCHAR(20) DEFAULT NULL COMMENT '借阅时间',
                                 `GHSJ` VARCHAR(20) DEFAULT NULL COMMENT '归还时间',
                                 `TSGZJH` VARCHAR(100) DEFAULT NULL COMMENT '图书馆证件号',
                                 `TSFLH` VARCHAR(100) DEFAULT NULL COMMENT '图书分类号',
                                 `TSTM` VARCHAR(100) DEFAULT NULL COMMENT '图书题名',
                                 `TSTAMP` VARCHAR(20) DEFAULT NULL COMMENT '时间戳',
                                 `CQTS` VARCHAR(10) DEFAULT NULL COMMENT '超期天数',
                                 PRIMARY KEY (`GID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='借阅历史信息';

-- ----------------------------
-- Records for T_GXTS_JYLSXX
-- ----------------------------
INSERT INTO `T_GXTS_JYLSXX` (`GID`, `XGH`, `JYSJ`, `GHSJ`, `TSGZJH`, `TSFLH`, `TSTM`, `TSTAMP`, `CQTS`) VALUES
                                                                                                            ('d8f3b3e4-2b0a-4a6e-9f3b-8c8a1b9e2d3f', '202211001', '2024-03-15 10:22:00', '2024-04-14 18:00:00', 'L202211001', 'TP312', '深入理解计算机系统', '2024-04-14 18:05:11', '0'),
                                                                                                            ('a1e9c8b7-6d5f-4e3c-2b1a-098f7e6d5c4b', '202211001', '2024-04-01 11:30:00', '2024-05-10 09:15:00', 'L202211001', 'I247.5', '三体', '2024-05-10 09:20:01', '9'),
                                                                                                            ('f7e6d5c4-b3a2-1987-6543-21098fedcba9', '202309055', '2024-04-20 14:00:00', '2024-05-20 11:45:00', 'L202309055', 'O1-09', '数学之美', '2024-05-20 11:50:23', '0'),
                                                                                                            ('b2a19876-5432-fedc-ba98-76543210fedc', '202103210', '2024-05-02 16:45:00', NULL, 'L202103210', 'H319.4', '现代汉语', '2024-05-02 16:51:30', '0'),
                                                                                                            ('c3d2e1f0-a9b8-c7d6-e5f4-a3b2c1d0e9f8', '202309055', '2024-05-10 09:00:00', '2024-05-18 10:00:00', 'L202309055', 'G252', '图书馆学概论', '2024-05-18 10:05:00', '0');
-- 7. 验证查询语句

-- 查询学生列表（带预警等级）
SELECT 
    b.XH as student_id,
    b.XM as name,
    CASE 
        WHEN MAX(CASE 
            WHEN w.YJDJ_M = '3' THEN 4
            WHEN w.YJDJ_M = '2' THEN 3
            WHEN w.YJDJ_M = '1' THEN 2
            ELSE 1
        END) = 4 THEN 'HIGH'
        WHEN MAX(CASE 
            WHEN w.YJDJ_M = '3' THEN 4
            WHEN w.YJDJ_M = '2' THEN 3
            WHEN w.YJDJ_M = '1' THEN 2
            ELSE 1
        END) = 3 THEN 'MEDIUM'
        WHEN MAX(CASE 
            WHEN w.YJDJ_M = '3' THEN 4
            WHEN w.YJDJ_M = '2' THEN 3
            WHEN w.YJDJ_M = '1' THEN 2
            ELSE 1
        END) = 2 THEN 'LOW'
        ELSE 'NONE'
    END as overall_warning_level
FROM T_GXXS_BKSJBXX b
LEFT JOIN T_GXYS_YJXX w ON b.XH = w.XH 
    AND w.YJZT_M NOT IN ('03', '04')
GROUP BY b.WYBS, b.XH, b.XM
ORDER BY b.XH;

-- 查询学生概览信息
SELECT 
    b.XM as name,
    b.XH as student_id,
    b.XBM as gender_code,
    COALESCE(org.ZZJGMC, '未知院系') as department_name,
    COALESCE(major.ZYMC, '未知专业') as major_name,
    COALESCE(class.ZYBBMC, '未知班级') as class_name,
    s.NJ as grade
FROM T_GXXS_BKSJBXX b
LEFT JOIN T_GXXS_BKSXJJBXX s ON b.XH = s.XH
LEFT JOIN DM_XB_ZZJGM org ON s.XDYXBM = org.ZZJGM
LEFT JOIN DM_XB_BKSZYM major ON s.ZYBM = major.ZYBM
LEFT JOIN DM_XB_BKSZYBJM class ON s.ZYBBM = class.ZYBBM
WHERE b.XH = '202310001';

-- 查询预警模块信息
SELECT 
    CASE 
        WHEN w.YJLX_M BETWEEN '01' AND '09' THEN 'ACADEMIC'
        WHEN w.YJLX_M BETWEEN '10' AND '19' THEN 'TRACK'
        WHEN w.YJLX_M BETWEEN '20' AND '29' THEN 'SOCIAL'
        WHEN w.YJLX_M BETWEEN '30' AND '39' THEN 'PSYCHOLOGICAL'
        ELSE 'OTHER'
    END as module_code,
    CASE 
        WHEN w.YJDJ_M = '3' THEN 'HIGH'
        WHEN w.YJDJ_M = '2' THEN 'MEDIUM'
        WHEN w.YJDJ_M = '1' THEN 'LOW'
        ELSE 'NONE'
    END as warning_level,
    w.YJMS as description
FROM T_GXYS_YJXX w
WHERE w.XH = '202310001'
AND w.YJZT_M NOT IN ('03', '04')
ORDER BY w.YJLX_M;
