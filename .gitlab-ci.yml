stages:
  - build

variables:
  REGISTRY_USER: mohi2
  REGISTRY_URL: "registry.cn-shanghai.aliyuncs.com"

services:
  - registry.cn-shanghai.aliyuncs.com/lf-pub/docker:dind

before_script:
  - echo "Hito@docker" | docker login -u $REGISTRY_USER --password-stdin $REGISTRY_URL

build:
  stage: build
  tags:
    - 802-local
  image: registry.cn-shanghai.aliyuncs.com/lf-pub/docker:latest
  rules:
    - if: '$CI_COMMIT_REF_PROTECTED && $CI_COMMIT_TAG'
      allow_failure: false
    - when: never
  script:
    - docker build -f Dockerfile -t $REGISTRY_URL/iai-lf/ewps-backend:$CI_COMMIT_TAG .
    - docker push $REGISTRY_URL/iai-lf/ewps-backend:$CI_COMMIT_TAG
    - docker tag $REGISTRY_URL/iai-lf/ewps-backend:$CI_COMMIT_TAG $REGISTRY_URL/iai-lf/ewps-backend:latest
    - docker push $REGISTRY_URL/iai-lf/ewps-backend:latest
    - docker rmi $REGISTRY_URL/iai-lf/ewps-backend:latest
    - docker rmi $REGISTRY_URL/iai-lf/ewps-backend:$CI_COMMIT_TAG