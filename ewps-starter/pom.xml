<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zkdm.iai</groupId>
        <artifactId>ewps-backend</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ewps-starter</artifactId>
    <description>项目启动入口</description>

    <dependencies>
        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zkdm.iai</groupId>
            <artifactId>ewps-common-core</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- OpenFeign 依赖 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!-- WebFlux for reactive streaming -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zkdm.iai</groupId>
            <artifactId>ewps-common-doc</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.zkdm.iai</groupId>
            <artifactId>ewps-common-web</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.zkdm.iai</groupId>
            <artifactId>ewps-common-mybatis</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.zkdm.iai</groupId>
            <artifactId>ewps-common-redis</artifactId>
            <version>${revision}</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven-jar-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>${maven-war-plugin.version}</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>