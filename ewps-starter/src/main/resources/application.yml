# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
spring:
  profiles:
    active: dev
  application:
    name: ustc-ewps-backend
  mybatis-plus:
    configuration:
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# SpringDoc配置
springdoc:
  api-docs:
    path: /v3/api-docs  # OpenAPI 规范的访问路径
  swagger-ui:
    path: /swagger-ui.html # Swagger UI 界面的访问路径
    # 自定义配置：
    title: 中科大早期预警感知系统 API (由springdoc生成) # 自定义UI页面的标题
    display-request-duration: true # 显示请求耗时

# AI聊天服务配置
ai:
  chat:
    base-url: http://10.100.157.113:82/v1
    api-key: app-Rb5qu3b8pihdp6t56h0XXEAy
    connect-timeout: 30000
    read-timeout: 60000
    default-user: ewps-system

# Feign配置
feign:
  client:
    config:
      default:
        connect-timeout: 30000
        read-timeout: 60000
        logger-level: basic
  compression:
    request:
      enabled: true
    response:
      enabled: true