<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zkdm.iai.mapper.student.StudentMapper">

    <!-- 获取学生列表（带预警次数） -->
    <select id="getStudentListWithWarningCount" resultMap="studentListResultMap">
        SELECT
            b.XM as name,
            b.XH as studentId,
            b.X<PERSON> as genderCode,
            IFNULL(org.ZZJGMC, '未知院系') as departmentName,
            IFNULL(major.ZYMC, '未知专业') as majorName,
            IFNULL(class.ZYBBMC, '未知班级') as className,
            IFNULL(s.NJ, '未知年级') as grade,
            IFNULL(warning_count.count, 0) as warningCount
        FROM T_GXXS_BKSJBXX b
        LEFT JOIN T_GXXS_BKSXJJBXX s ON b.XH = s.XH
        LEFT JOIN DM_XB_ZZJGM org ON s.XDYXBM = org.ZZJGM
        LEFT JOIN DM_XB_BKSZYM major ON s.ZYBM = major.ZYM
        LEFT JOIN DM_XB_BKSZYBJM class ON s.ZYBBM = class.ZYBBM
        LEFT JOIN (
            SELECT
                XH,
                COUNT(*) as count
            FROM T_GXYS_YJXX
            WHERE YJZT_M NOT IN ('03', '04')
            GROUP BY XH
        ) warning_count ON b.XH = warning_count.XH
        <where>
            <if test="query != null and query != ''">
                AND (b.XM LIKE CONCAT('%', #{query}, '%') OR b.XH LIKE CONCAT('%', #{query}, '%'))
            </if>
        </where>
        ORDER BY b.XH
    </select>

    <!-- StudentListVo结果映射 -->
    <resultMap id="studentListResultMap" type="com.zkdm.iai.domain.vo.student.StudentListVo">
        <result property="warningCount" column="warningCount"/>
        <association property="profile" javaType="com.zkdm.iai.domain.vo.student.ProfileVo">
            <result property="name" column="name"/>
            <result property="studentId" column="studentId"/>
            <result property="genderCode" column="genderCode"/>
            <result property="departmentName" column="departmentName"/>
            <result property="majorName" column="majorName"/>
            <result property="className" column="className"/>
            <result property="grade" column="grade"/>
        </association>
    </resultMap>

    <!-- 获取学生详细概览信息 -->
    <select id="getStudentOverview" resultType="com.zkdm.iai.domain.vo.student.ProfileVo">
        SELECT
            b.XM as name,
            b.XH as studentId,
            b.XBM as genderCode,
            IFNULL(org.ZZJGMC, '未知院系') as departmentName,
            IFNULL(major.ZYMC, '未知专业') as majorName,
            IFNULL(class.ZYBBMC, '未知班级') as className,
            IFNULL(s.NJ, '未知年级') as grade
        FROM T_GXXS_BKSJBXX b
        LEFT JOIN T_GXXS_BKSXJJBXX s ON b.XH = s.XH
        LEFT JOIN DM_XB_ZZJGM org ON s.XDYXBM = org.ZZJGM
        LEFT JOIN DM_XB_BKSZYM major ON s.ZYBM = major.ZYM
        LEFT JOIN DM_XB_BKSZYBJM class ON s.ZYBBM = class.ZYBBM
        WHERE b.XH = #{studentId}
    </select>

    <!-- 获取学生的预警模块信息 -->
    <select id="getStudentWarningModules" resultType="com.zkdm.iai.domain.vo.student.WarningModuleVo">
        SELECT
            modules.moduleCode,
            modules.moduleName,
            IFNULL(warnings.level, 'NONE') as level,
            IF(warnings.level IS NOT NULL AND warnings.level != 'NONE', modules.warningStatus, modules.defaultStatus) as statusText
        FROM (
            SELECT 'ACADEMIC' as moduleCode, '学业预警' as moduleName, '学业表现良好' as defaultStatus, '存在学业风险' as warningStatus, 1 as sortOrder
            UNION ALL
            SELECT 'TRACK' as moduleCode, '轨迹预警' as moduleName, '行为轨迹正常' as defaultStatus, '高度关注' as warningStatus, 2 as sortOrder
            UNION ALL
            SELECT 'SOCIAL' as moduleCode, '生活预警' as moduleName, '生活状态良好' as defaultStatus, '活跃度行为满意' as warningStatus, 3 as sortOrder
            UNION ALL
            SELECT 'PSYCHOLOGICAL' as moduleCode, '心理预警' as moduleName, '心理状态健康' as defaultStatus, '暂无风险' as warningStatus, 4 as sortOrder
        ) modules
        LEFT JOIN (
            SELECT
                'ACADEMIC' as moduleCode,
                CASE
                    WHEN MAX(CAST(YJDJ_M AS UNSIGNED)) = 3 THEN 'HIGH'
                    WHEN MAX(CAST(YJDJ_M AS UNSIGNED)) = 2 THEN 'MEDIUM'
                    WHEN MAX(CAST(YJDJ_M AS UNSIGNED)) = 1 THEN 'LOW'
                    ELSE 'NONE'
                END as level
            FROM T_GXYS_YJXX
            WHERE XH = #{studentId} AND YJZT_M NOT IN ('03', '04') AND YJLX_M LIKE '0%'

            UNION ALL

            SELECT
                'TRACK' as moduleCode,
                CASE
                    WHEN MAX(CAST(YJDJ_M AS UNSIGNED)) = 3 THEN 'HIGH'
                    WHEN MAX(CAST(YJDJ_M AS UNSIGNED)) = 2 THEN 'MEDIUM'
                    WHEN MAX(CAST(YJDJ_M AS UNSIGNED)) = 1 THEN 'LOW'
                    ELSE 'NONE'
                END as level
            FROM T_GXYS_YJXX
            WHERE XH = #{studentId} AND YJZT_M NOT IN ('03', '04') AND YJLX_M LIKE '1%'

            UNION ALL

            SELECT
                'SOCIAL' as moduleCode,
                CASE
                    WHEN MAX(CAST(YJDJ_M AS UNSIGNED)) = 3 THEN 'HIGH'
                    WHEN MAX(CAST(YJDJ_M AS UNSIGNED)) = 2 THEN 'MEDIUM'
                    WHEN MAX(CAST(YJDJ_M AS UNSIGNED)) = 1 THEN 'LOW'
                    ELSE 'NONE'
                END as level
            FROM T_GXYS_YJXX
            WHERE XH = #{studentId} AND YJZT_M NOT IN ('03', '04') AND YJLX_M LIKE '2%'

            UNION ALL

            SELECT
                'PSYCHOLOGICAL' as moduleCode,
                CASE
                    WHEN MAX(CAST(YJDJ_M AS UNSIGNED)) = 3 THEN 'HIGH'
                    WHEN MAX(CAST(YJDJ_M AS UNSIGNED)) = 2 THEN 'MEDIUM'
                    WHEN MAX(CAST(YJDJ_M AS UNSIGNED)) = 1 THEN 'LOW'
                    ELSE 'NONE'
                END as level
            FROM T_GXYS_YJXX
            WHERE XH = #{studentId} AND YJZT_M NOT IN ('03', '04') AND YJLX_M LIKE '3%'
        ) warnings ON modules.moduleCode = warnings.moduleCode
        ORDER BY modules.sortOrder
    </select>

</mapper>
