spring:
  datasource:
    driver-class-name: com.p6spy.engine.spy.P6SpyDriver
    url: ***********************************************************************************************************************************************
    username: root
    password: root
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
  redis:
    redisson:
      config: |
        singleServerConfig:
          address: redis://**************:6377
          timeout: 3000
          password: redis_ztPDxc

# 如果需要，也可以把 management 等其他配置放进来
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus