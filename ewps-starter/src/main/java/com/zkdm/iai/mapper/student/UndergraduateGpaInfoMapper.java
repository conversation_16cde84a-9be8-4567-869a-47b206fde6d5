package com.zkdm.iai.mapper.student;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zkdm.iai.domain.dto.StudentGpaInfoDto;
import com.zkdm.iai.domain.entity.student.UndergraduateGpaInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface UndergraduateGpaInfoMapper extends BaseMapper<UndergraduateGpaInfo> {

    /**
     * 根据学号查询学生基本信息（临时方案，直接从基本信息表获取）
     *
     * @param studentId 学号
     * @return 学生GPA信息DTO
     */
    @Select("""
        SELECT
            g.XH as studentId,
            b.XM as name,
            g.ZXF as totalCredits,
            g.GPA as gpa,
            g.JQPJF as weightedAverageScore,
            g.SSPJF as arithmeticAverageScore,
            g.TGZXF as passedTotalCredits,
            g.WTCZXF as failedTotalCredits,
            g.GPAJSFS as gpaCalculationMethod,
            g.SFCYPM as participateInRanking,
            g.GXRQSJ as updateStartTime
        FROM T_GXXS_BKSQCJDXX g
        LEFT JOIN T_GXXS_BKSJBXX b ON g.XH = b.XH
        WHERE g.XH = #{studentId}
        LIMIT 1
        """)
    StudentGpaInfoDto selectLatestGpaInfoWithBasicInfo(@Param("studentId") String studentId);
}
