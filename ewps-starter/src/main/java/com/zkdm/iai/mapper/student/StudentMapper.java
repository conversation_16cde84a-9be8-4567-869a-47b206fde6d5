package com.zkdm.iai.mapper.student;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zkdm.iai.domain.vo.student.ProfileVo;
import com.zkdm.iai.domain.vo.student.StudentListVo;
import com.zkdm.iai.domain.vo.student.StudentOverviewVo;
import com.zkdm.iai.domain.vo.student.WarningModuleVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学生信息复杂查询Mapper
 * 用于处理需要多表关联和复杂逻辑的查询
 */
@Mapper
public interface StudentMapper {

    /**
     * 获取学生列表（带预警次数）
     *
     * @param page 分页参数
     * @param query 搜索关键词
     * @return 学生列表分页数据
     */
    IPage<StudentListVo> getStudentListWithWarningCount(Page<?> page, @Param("query") String query);

    /**
     * 获取学生详细概览信息
     *
     * @param studentId 学生学号
     * @return 学生档案信息
     */
    ProfileVo getStudentOverview(@Param("studentId") String studentId);

    /**
     * 获取学生的预警模块信息
     * 
     * @param studentId 学生学号
     * @return 预警模块列表
     */
    List<WarningModuleVo> getStudentWarningModules(@Param("studentId") String studentId);
}
