package com.zkdm.iai.service.student;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zkdm.iai.domain.entity.student.UndergraduateBasicInfo;

import com.zkdm.iai.domain.vo.student.StudentListVo;
import com.zkdm.iai.domain.vo.student.StudentOverviewVo;


public interface IStudentService {

    /**
     * 获取带有预警次数的学生分页列表。
     *
     * @param page  分页信息。
     * @param query 用于学号或姓名搜索的关键词。
     * @return 包含学生列表数据的分页对象。
     */
    IPage<StudentListVo> getStudentList(Page<UndergraduateBasicInfo> page, String query);

    /**
     * 获取单个学生的综合概览信息。
     *
     * @param studentId 学生的学号。
     * @return 学生的概览数据。
     */
    StudentOverviewVo getStudentOverview(String studentId);
}