package com.zkdm.iai.provider.impl;

import com.zkdm.iai.domain.entity.library.LibraryGateAccessLog;
import com.zkdm.iai.domain.vo.AcademicInfoVO;
import com.zkdm.iai.domain.vo.LibraryAccessVO;
import com.zkdm.iai.mapper.library.LibraryGateAccessLogMapper;
import com.zkdm.iai.mybatis.utils.MpQueryUtils;
import com.zkdm.iai.provider.AcademicInfoProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.function.Consumer;

/**
 * 图书馆出入次数信息提供者实现类
 */
@Component
@RequiredArgsConstructor
class LibraryAccessProviderImpl implements AcademicInfoProvider {
    private final LibraryGateAccessLogMapper accessLogMapper;
    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(String studentId) {
        // TODO: 真实实现应按当前学期的日期范围进行筛选
        var query = MpQueryUtils.<LibraryGateAccessLog>lambdaQuery()
                .eq(LibraryGateAccessLog::getUserNumber, studentId).build();
        long count = accessLogMapper.selectCount(query);
        // TODO: 与上学期对比的逻辑需要另外实现
        var libraryAccessVO = LibraryAccessVO.builder().count(count + "次").comparison("较上学期 -1次").build();
        return builder -> builder.libraryAccessInfo(libraryAccessVO);
    }
}