package com.zkdm.iai.provider;

import com.zkdm.iai.domain.vo.AcademicInfoVO;
import org.springframework.lang.NonNull;
import java.util.function.Consumer;

/**
 * 策略接口：为学生提供一项具体的学业信息。
 * <p>
 * [优化] 此接口返回一个Consumer函数，该函数封装了如何将获取到的数据设置到总VO的Builder中的行为。
 * 函数式设计使得Service层可以统一处理所有提供者，无需关心其具体实现。
 */
@FunctionalInterface
public interface AcademicInfoProvider {

    /**
     * 为指定的学生提供一项学业指标的“构建行为”。
     *
     * @param studentId 学生的唯一标识（学号）。
     * @return 一个Consumer函数，它接收一个AcademicInfoVO.Builder并对其进行配置。
     */
    Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(@NonNull String studentId);
}