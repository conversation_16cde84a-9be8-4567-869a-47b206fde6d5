package com.zkdm.iai.provider.impl;

import com.zkdm.iai.domain.entity.student.UndergraduateScholarship;
import com.zkdm.iai.domain.vo.AcademicInfoVO;
import com.zkdm.iai.domain.vo.AwardsVO;
import com.zkdm.iai.mapper.student.UndergraduateScholarshipMapper;
import com.zkdm.iai.mybatis.utils.MpQueryUtils;
import com.zkdm.iai.provider.AcademicInfoProvider;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.function.Consumer;

/**
 * 获奖情况信息提供者实现类
 */
@Component
@RequiredArgsConstructor
class AwardsProviderImpl implements AcademicInfoProvider {
    private final UndergraduateScholarshipMapper scholarshipMapper;
    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(String studentId) {
        var query = MpQueryUtils.<UndergraduateScholarship>lambdaQuery()
                .eq(UndergraduateScholarship::getStudentId, studentId).build();
        long count = scholarshipMapper.selectCount(query);
        // TODO: 与上学期对比的逻辑需要另外实现
        var awardsVO = AwardsVO.builder().count(count + "项").comparison("较上学期 -1次").build();
        return builder -> builder.awardsInfo(awardsVO);
    }
}