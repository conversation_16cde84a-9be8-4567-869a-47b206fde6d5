package com.zkdm.iai.provider.impl;

import com.zkdm.iai.domain.vo.AcademicInfoVO;
import com.zkdm.iai.domain.vo.AttendanceVO;
import com.zkdm.iai.provider.AcademicInfoProvider;
import org.springframework.stereotype.Component;

import java.util.function.Consumer;

/**
 * 出勤率信息提供者实现类
 */
@Component
class AttendanceProviderImpl implements AcademicInfoProvider {
    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(String studentId) {
        // TODO: 出勤率计算逻辑复杂，此处为模拟数据
        var attendanceVO = AttendanceVO.builder().rate("80%").comparison("较班级平均值低于5%").build();
        return builder -> builder.attendanceInfo(attendanceVO);
    }
}