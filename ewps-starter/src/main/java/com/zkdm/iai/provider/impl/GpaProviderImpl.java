package com.zkdm.iai.provider.impl;

import com.zkdm.iai.domain.dto.StudentGpaInfoDto;
import com.zkdm.iai.domain.vo.AcademicInfoVO;
import com.zkdm.iai.domain.vo.GpaVO;
import com.zkdm.iai.mapper.student.UndergraduateGpaInfoMapper;
import com.zkdm.iai.provider.AcademicInfoProvider;
import com.zkdm.iai.service.impl.MockGpaDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.function.Consumer;

/**
 * GPA信息提供者实现类
 */
@Slf4j
@Component
@RequiredArgsConstructor
class GpaProviderImpl implements AcademicInfoProvider {
    private final UndergraduateGpaInfoMapper gpaInfoMapper;
    private final MockGpaDataService mockGpaDataService;
    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(String studentId) {
        StudentGpaInfoDto gpaInfo = null;

        try {
            // 尝试从数据库查询
            gpaInfo = gpaInfoMapper.selectLatestGpaInfoWithBasicInfo(studentId);
        } catch (Exception e) {
            log.warn("从数据库查询GPA信息失败，使用模拟数据: {}", e.getMessage());
        }

        // 如果数据库查询失败，使用模拟数据
        if (gpaInfo == null) {
            gpaInfo = mockGpaDataService.getStudentGpaInfo(studentId);
        }

        final StudentGpaInfoDto finalGpaInfo = gpaInfo;
        GpaVO gpaVO = Optional.ofNullable(finalGpaInfo).map(info -> GpaVO.builder()
                .currentGpa(info.getGpa())
                .maxGpa("4")
                // TODO: 与上学期对比需要查询上学期的数据点，此处为模拟数据
                .comparison("较上学期 +10%")
                .build()
        ).orElseGet(() -> GpaVO.builder().currentGpa("0.0").maxGpa("4").comparison("暂无数据").build());
        return builder -> builder.gpaInfo(gpaVO);
    }
}