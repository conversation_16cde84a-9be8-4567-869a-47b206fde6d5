package com.zkdm.iai.domain.vo.student;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 学生列表视图对象
 * <AUTHOR>
 */
@Schema(description = "学生列表项")
@Data
public class StudentListVo {

    @Schema(description = "学生档案信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProfileVo profile;

    @Schema(description = "预警总次数", example = "3")
    private Integer warningCount;
}