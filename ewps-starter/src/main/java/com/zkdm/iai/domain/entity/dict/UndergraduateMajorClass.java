package com.zkdm.iai.domain.entity.dict;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：本科生专业班级码 (DM_XB_BKSZYBJM)
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("DM_XB_BKSZYBJM")
public class UndergraduateMajorClass {

    /**
     * 代码 (专业班编码)
     */
    @TableId("ZYBBM")
    private String majorClassCode;

    /**
     * 代码名称 (专业班名称)
     */
    @TableField("ZYBBMC")
    private String majorClassName;

    /**
     * 年级
     */
    @TableField("NJ")
    private String grade;

    /**
     * 是否启用 (1:是, 0:否)
     */
    @TableField("SFQY")
    private Boolean enabled;

    /**
     * 备注
     */
    @TableField("BZ")
    private String remarks;

    /**
     * 时间戳
     */
    @TableField("SJCS")
    private String timestamp;
}
