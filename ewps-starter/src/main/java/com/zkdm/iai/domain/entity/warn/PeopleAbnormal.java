package com.zkdm.iai.domain.entity.warn;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 实体类：人员异常行为表 (people_abnormal)
 * <AUTHOR>
 */
@Schema(description = "人员异常行为信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("people_abnormal")
public class PeopleAbnormal {

    @Schema(description = "异常发生时间", example = "2023-10-27T15:45:10", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("moment")
    private LocalDateTime moment;

    @Schema(description = "学工号", example = "202310001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("xgh")
    private String userNumber;

    @Schema(description = "异常发生时间戳（13位）", example = "1698392710000", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("timestamp")
    private Long timestamp;

    @Schema(description = "异常发生精确位置-经度", example = "116.397128", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("longitude")
    private Double longitude;

    @Schema(description = "异常发生精确位置-纬度", example = "39.916527", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("latitude")
    private Double latitude;

    @Schema(description = "校区名称", example = "主校区", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("xqmc")
    private String campusName;
    
    @Schema(description = "楼栋名称", example = "实验楼B座", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("building_name")
    private String buildingName;

    @Schema(description = "摄像头名称", example = "B座三楼走廊-03", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("camera_name")
    private String cameraName;

    @Schema(description = "异常类型", example = "3", requiredMode = Schema.RequiredMode.REQUIRED, allowableValues = {"0", "1", "2", "3", "4", "5", "6", "7", "99"})
    @TableField("type")
    private Integer type;

    @Schema(description = "异常等级", example = "2", requiredMode = Schema.RequiredMode.REQUIRED, allowableValues = {"0", "1", "2", "3"})
    @TableField("rank")
    private Integer rank;

    @Schema(description = "异常状态", example = "0", requiredMode = Schema.RequiredMode.REQUIRED, allowableValues = {"0", "1"})
    @TableField("status")
    private Integer status;

    @Schema(description = "处理人", example = "辅导员-王老师")
    @TableField("handler")
    private String handler;

    @Schema(description = "处理时间", example = "2023-10-27T16:00:00")
    @TableField("handle_time")
    private LocalDateTime handleTime;

    @Schema(description = "证据图片访问路径", example = "/images/abnormal/20231027/xyz.jpg")
    @TableField("img_path")
    private String imagePath;

    @Schema(description = "位置类型", example = "0", requiredMode = Schema.RequiredMode.REQUIRED, allowableValues = {"0", "1"})
    @TableField("position_type")
    private Integer positionType;

    @Schema(description = "数据来源", example = "1", requiredMode = Schema.RequiredMode.REQUIRED, allowableValues = {"0", "1", "2"})
    @TableField("data_source")
    private Integer dataSource;

    @Schema(description = "备注信息", example = "检测到学生在实验楼B座三楼走廊摔倒，持续15秒未起身。")
    @TableField("memo")
    private String memo;
}