package com.zkdm.iai.domain.vo.student;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 预警模块视图对象
 * <AUTHOR>
 */
@Schema(description = "预警模块信息")
@Data
@Builder
public class WarningModuleVo {

    @Schema(description = "模块代码", example = "ACADEMIC",
            allowableValues = {"ACADEMIC", "TRACK", "SOCIAL", "PSYCHOLOGICAL"},
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String moduleCode;

    @Schema(description = "模块名称", example = "学业预警",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String moduleName;

    @Schema(description = "状态描述", example = "学业表现良好",
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String statusText;

    @Schema(description = "预警等级", example = "HIGH",
            allowableValues = {"NONE", "LOW", "MEDIUM", "HIGH"},
            requiredMode = Schema.RequiredMode.REQUIRED)
    private String level;
}