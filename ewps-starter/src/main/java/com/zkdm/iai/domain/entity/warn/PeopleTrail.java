package com.zkdm.iai.domain.entity.warn;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 实体类：人员轨迹表 (people_trail)
 * <AUTHOR>
 */
@Schema(description = "人员轨迹信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("people_trail")
public class PeopleTrail {

    @Schema(description = "轨迹点识别时间", example = "2023-10-27T10:30:00", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("moment")
    private LocalDateTime moment;

    @Schema(description = "学工号", example = "202310001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("xgh")
    private String userNumber;

    @Schema(description = "轨迹点识别时间戳（13位）", example = "1698373800000", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("timestamp")
    private Long timestamp;

    @Schema(description = "轨迹发生精确位置-经度", example = "116.397128")
    @TableField("longitude")
    private Double longitude;

    @Schema(description = "轨迹发生精确位置-纬度", example = "39.916527")
    @TableField("latitude")
    private Double latitude;

    @Schema(description = "校区名称", example = "主校区", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("xqmc")
    private String campusName;

    @Schema(description = "区域名称", example = "第一教学楼", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("mkmc")
    private String regionName;

    @Schema(description = "证据图片访问路径", example = "/images/trail/20231027/abc.jpg")
    @TableField("img_path")
    private String imagePath;

    @Schema(description = "位置类型", example = "位置类型：0：室内，1：室外", requiredMode = Schema.RequiredMode.REQUIRED, allowableValues = {"0", "1"})
    @TableField("position_type")
    private Integer positionType;

    @Schema(description = "数据来源", example = "数据来源：0：一卡通，1：视频分析算法，2：微波感知算法", requiredMode = Schema.RequiredMode.REQUIRED, allowableValues = {"0", "1", "2"})
    @TableField("data_source")
    private Integer dataSource;

    @Schema(description = "备注信息", example = "从东门进入")
    @TableField("memo")
    private String memo;
}