package com.zkdm.iai.domain.entity.student;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：本科生基本信息 (T_GXXS_BKSJBXX)
 * 所属部门: 教务处
 * <AUTHOR>
 */
@Schema(description = "本科生基本信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_GXXS_BKSJBXX")
public class UndergraduateBasicInfo {

    /**
     * 唯一标识 (主键)
     */
    @Schema(description = "唯一标识", example = "1001")
    @TableId(value = "WYBS", type = IdType.INPUT)
    private String id;

    /**
     * 学号 (唯一)
     */
    @Schema(description = "学号", example = "202310001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("XH")
    private String studentId;

    /**
     * 姓名
     */
    @Schema(description = "姓名", example = "张三", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("XM")
    private String name;

    /**
     * 外文姓名
     */
    @Schema(description = "外文姓名", example = "Zhang San")
    @TableField("WWXM")
    private String foreignName;

    /**
     * 性别码 (关联代码表: DM_XB_XBM)
     */
    @Schema(description = "性别码", example = "1", allowableValues = {"1", "2"})
    @TableField("XBM")
    private String genderCode;

    /**
     * 曾用名
     */
    @Schema(description = "曾用名", example = "张小三")
    @TableField("CYM")
    private String formerName;

    /**
     * 籍贯省份码
     */
    @Schema(description = "籍贯省份码", example = "34")
    @TableField("JGSFM")
    private String nativeProvinceCode;

    /**
     * 籍贯(其他)
     */
    @Schema(description = "籍贯(其他)", example = "安徽合肥")
    @TableField("JGQT")
    private String nativeOther;

    /**
     * 健康状况
     */
    @Schema(description = "健康状况", example = "健康")
    @TableField("JKZK")
    private String healthStatus;

    /**
     * 出生日期
     */
    @Schema(description = "出生日期", example = "2005-01-01", format = "date")
    @TableField("CSRQ")
    private String birthDate;

    /**
     * 民族码
     */
    @Schema(description = "民族码", example = "01")
    @TableField("MZM")
    private String ethnicityCode;

    /**
     * 国籍/地区码
     */
    @Schema(description = "国籍/地区码", example = "156")
    @TableField("GJDQM")
    private String countryRegionCode;

    /**
     * 身份证件类型码
     */
    @Schema(description = "身份证件类型码", example = "1")
    @TableField("SFZJLXM")
    private String idTypeCode;

    /**
     * 身份证件号
     */
    @Schema(description = "身份证件号", example = "340123200501010001")
    @TableField("SFZJH")
    private String idNumber;

    /**
     * 政治面貌码
     */
    @Schema(description = "政治面貌码", example = "01")
    @TableField("ZZMMM")
    private String politicalStatusCode;

    /**
     * 邮政编码
     */
    @Schema(description = "邮政编码", example = "230026", pattern = "^\\d{6}$")
    @TableField("YZBM")
    private String postalCode;

    /**
     * 联系地址
     */
    @Schema(description = "联系地址", example = "安徽省合肥市包河区金寨路96号")
    @TableField("LXDZ")
    private String contactAddress;

    /**
     * 固定电话
     */
    @Schema(description = "固定电话", example = "0551-63602553")
    @TableField("GDDH")
    private String fixedPhone;

    /**
     * 移动电话
     */
    @Schema(description = "移动电话", example = "13800138000", pattern = "^1[3-9]\\d{9}$")
    @TableField("YDDH")
    private String mobilePhone;

    /**
     * 电子邮箱
     */
    @Schema(description = "电子邮箱", example = "<EMAIL>", format = "email")
    @TableField("DZYX")
    private String email;

    /**
     * 即时通讯QQ号
     */
    @Schema(description = "即时通讯QQ号", example = "123456789")
    @TableField("JSTXQQH")
    private String qqNumber;

    /**
     * 即时通讯微信号
     */
    @Schema(description = "即时通讯微信号", example = "zhangsan_wx")
    @TableField("JSTXWXH")
    private String wechatNumber;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳", example = "2023-10-01 10:00:00", format = "date-time")
    @TableField("TTIMESTAMP")
    private String timestamp;
}
