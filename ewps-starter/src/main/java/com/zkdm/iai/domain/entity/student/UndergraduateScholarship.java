package com.zkdm.iai.domain.entity.student;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：本科生奖学金信息 (T_GXXS_BKSJXJXX)
 * 所属部门: 学工处
 * <AUTHOR>
 */
@Schema(description = "本科生奖学金信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_GXXS_BKSJXJXX")
public class UndergraduateScholarship {

    /**
     * 唯一标识 (主键)
     */
    @Schema(description = "唯一标识", example = "1001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableId(value = "WYBS", type = IdType.INPUT)
    private String id;

    /**
     * 学号
     */
    @Schema(description = "学号", example = "202310001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("XH")
    private String studentId;

    /**
     * 姓名
     */
    @Schema(description = "姓名", example = "张三")
    @TableField("XM")
    private String name;

    /**
     * 奖学金名称
     */
    @Schema(description = "奖学金名称", example = "国家奖学金")
    @TableField("JXJMC")
    private String scholarshipName;

    /**
     * 奖学金类型码
     */
    @Schema(description = "奖学金类型码", example = "01", 
            allowableValues = {"01", "02", "03", "04", "05"})
    @TableField("JXJLXM")
    private String scholarshipTypeCode;

    /**
     * 奖学金等级码
     */
    @Schema(description = "奖学金等级码", example = "1", 
            allowableValues = {"1", "2", "3"})
    @TableField("JXJDJM")
    private String scholarshipLevelCode;

    /**
     * 奖学金金额
     */
    @Schema(description = "奖学金金额", example = "8000.00", minimum = "0")
    @TableField("JXJJE")
    private String scholarshipAmount;

    /**
     * 获奖学年
     */
    @Schema(description = "获奖学年", example = "2023-2024")
    @TableField("HJXN")
    private String awardAcademicYear;

    /**
     * 获奖学期
     */
    @Schema(description = "获奖学期", example = "2023-2024-1")
    @TableField("HJXQ")
    private String awardSemester;

    /**
     * 获奖日期
     */
    @Schema(description = "获奖日期", example = "2023-12-01", format = "date")
    @TableField("HJRQ")
    private String awardDate;

    /**
     * 发放状态码
     */
    @Schema(description = "发放状态码", example = "1", 
            allowableValues = {"0", "1", "2"})
    @TableField("FFZTM")
    private String distributionStatusCode;

    /**
     * 发放日期
     */
    @Schema(description = "发放日期", example = "2023-12-15", format = "date")
    @TableField("FFRQ")
    private String distributionDate;

    /**
     * 审核状态码
     */
    @Schema(description = "审核状态码", example = "2", 
            allowableValues = {"0", "1", "2"})
    @TableField("SHZTM")
    private String auditStatusCode;

    /**
     * 审核人
     */
    @Schema(description = "审核人", example = "李老师")
    @TableField("SHR")
    private String auditor;

    /**
     * 审核日期
     */
    @Schema(description = "审核日期", example = "2023-11-20", format = "date")
    @TableField("SHRQ")
    private String auditDate;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "表现优秀，成绩突出")
    @TableField("BZ")
    private String remarks;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳", example = "2023-10-01 10:00:00", format = "date-time")
    @TableField("TTIMESTAMP")
    private String timestamp;

    /**
     * 学号SM4加密
     */
    @Schema(description = "学号SM4加密", hidden = true)
    @TableField("XHSM4")
    private String studentIdSm4;

    /**
     * 院系编码
     */
    @Schema(description = "院系编码", example = "CS001")
    @TableField("YXBM")
    private String departmentCode;

    /**
     * 专业编码
     */
    @Schema(description = "专业编码", example = "080901")
    @TableField("ZYBM")
    private String majorCode;

    /**
     * 班级编码
     */
    @Schema(description = "班级编码", example = "CS2023-01")
    @TableField("BJBM")
    private String classCode;

    /**
     * 年级
     */
    @Schema(description = "年级", example = "2023")
    @TableField("NJ")
    private String grade;

    /**
     * 申请理由
     */
    @Schema(description = "申请理由", example = "学习成绩优异，综合素质突出")
    @TableField("SQLY")
    private String applicationReason;

    /**
     * 推荐人
     */
    @Schema(description = "推荐人", example = "王教授")
    @TableField("TJR")
    private String recommender;
}
