package com.zkdm.iai.domain.vo.student;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 学生档案视图对象
 */
@Schema(description = "学生档案信息")
@Data
public class ProfileVo {

    @Schema(description = "姓名", example = "张三", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "学号", example = "202310001", requiredMode = Schema.RequiredMode.REQUIRED)
    private String studentId;

    @Schema(description = "性别码", example = "1", allowableValues = {"1", "2"})
    private String genderCode;

    @Schema(description = "院系名称", example = "计算机科学与技术学院")
    private String departmentName;

    @Schema(description = "专业名称", example = "计算机科学与技术")
    private String majorName;

    @Schema(description = "班级名称", example = "计算机2023-1班")
    private String className;

    @Schema(description = "年级", example = "2023")
    private String grade;
}