package com.zkdm.iai.domain.entity.ecard;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：一卡通账户余额信息 (T_GXYKT_ZHYEXX)
 * 所属部门: 网络信息中心
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_GXYKT_ZHYEXX")
public class EcardAccountBalance {

    @TableId(value = "WYBS", type = IdType.INPUT)
    private String id;

    /**
     * 交易卡号
     */
    @TableField("JYKH")
    private String transactionCardNumber;

    /**
     * 钱包号
     */
    @TableField("QBH")
    private String walletNumber;

    /**
     * 交易流水
     */
    @TableField("JYLSH")
    private String transactionSerial;

    /**
     * 账户余额
     */
    @TableField("ZHYE")
    private String accountBalance;

    /**
     * 处理日期
     */
    @TableField("CLRQ")
    private String processingDate;

    /**
     * 卡片当前状态码
     */
    @TableField("KPDQZTM")
    private String cardCurrentStatusCode;

    /**
     * 时间戳
     */
    @TableField("TTIMESTAMP")
    private String timestamp;
}