package com.zkdm.iai.domain.vo.student;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * 学生概览视图对象
 */
@Schema(description = "学生概览信息")
@Data
public class StudentOverviewVo {

    @Schema(description = "学生档案信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProfileVo profile;

    @Schema(description = "预警模块列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<WarningModuleVo> warningModules;
}