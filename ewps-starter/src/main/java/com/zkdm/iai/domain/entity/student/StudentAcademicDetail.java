package com.zkdm.iai.domain.entity.student;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：学生学业信息 (T_GXJX_XSXYXX)
 * 所属部门: 教务处
 * <AUTHOR>
 */
@Schema(description = "学生学业信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_GXJX_XSXYXX")
public class StudentAcademicDetail {

    /**
     * 唯一标识 (主键)
     */
    @Schema(description = "唯一标识", example = "1001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableId(value = "WYBS", type = IdType.INPUT)
    private String id;

    /**
     * 学期
     */
    @Schema(description = "学期", example = "2023-2024-1")
    @TableField("XQ")
    private String semester;

    /**
     * 业务类型
     */
    @Schema(description = "业务类型", example = "01")
    @TableField("YWLX")
    private String businessType;

    /**
     * 教学任务编码
     */
    @Schema(description = "教学任务编码", example = "TASK001")
    @TableField("JXRWBM")
    private String teachingTaskCode;

    /**
     * 教学任务名称
     */
    @Schema(description = "教学任务名称", example = "高等数学A")
    @TableField("JXRWMC")
    private String teachingTaskName;

    /**
     * 课程编码
     */
    @Schema(description = "课程编码", example = "MATH001")
    @TableField("KCBM")
    private String courseCode;

    /**
     * 课程名称
     */
    @Schema(description = "课程名称", example = "高等数学A")
    @TableField("KCMC")
    private String courseName;

    /**
     * 课程类型码
     */
    @Schema(description = "课程类型码", example = "01", 
            allowableValues = {"01", "02", "03", "04"})
    @TableField("KCLXM")
    private String courseTypeCode;

    /**
     * 课程性质码
     */
    @Schema(description = "课程性质码", example = "1", 
            allowableValues = {"1", "2", "3"})
    @TableField("KCXZM")
    private String courseNatureCode;

    /**
     * 学分
     */
    @Schema(description = "学分", example = "4.0", minimum = "0")
    @TableField("XF")
    private String credits;

    /**
     * 总学时
     */
    @Schema(description = "总学时", example = "64", minimum = "0")
    @TableField("ZXS")
    private String totalHours;

    /**
     * 理论学时
     */
    @Schema(description = "理论学时", example = "48", minimum = "0")
    @TableField("LLXS")
    private String theoryHours;

    /**
     * 实验学时
     */
    @Schema(description = "实验学时", example = "16", minimum = "0")
    @TableField("SYXS")
    private String experimentHours;

    /**
     * 学号
     */
    @Schema(description = "学号", example = "202310001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("XH")
    private String studentId;

    /**
     * 姓名
     */
    @Schema(description = "姓名", example = "张三")
    @TableField("XM")
    private String name;

    /**
     * 平时成绩
     */
    @Schema(description = "平时成绩", example = "85", minimum = "0", maximum = "100")
    @TableField("PSCJ")
    private String regularScore;

    /**
     * 期中成绩
     */
    @Schema(description = "期中成绩", example = "88", minimum = "0", maximum = "100")
    @TableField("QZCJ")
    private String midtermScore;

    /**
     * 期末成绩
     */
    @Schema(description = "期末成绩", example = "92", minimum = "0", maximum = "100")
    @TableField("QMCJ")
    private String finalScore;

    /**
     * 总评成绩
     */
    @Schema(description = "总评成绩", example = "89", minimum = "0", maximum = "100")
    @TableField("ZPCJ")
    private String totalScore;

    /**
     * 绩点
     */
    @Schema(description = "绩点", example = "3.7", minimum = "0", maximum = "4")
    @TableField("JD")
    private String gradePoint;

    /**
     * 成绩等级
     */
    @Schema(description = "成绩等级", example = "A", 
            allowableValues = {"A", "B", "C", "D", "F"})
    @TableField("CJDJ")
    private String gradeLevel;

    /**
     * 是否通过
     */
    @Schema(description = "是否通过", example = "1", allowableValues = {"0", "1"})
    @TableField("SFTG")
    private String isPassed;

    /**
     * 考试类型码
     */
    @Schema(description = "考试类型码", example = "01")
    @TableField("KSLXM")
    private String examTypeCode;

    /**
     * 考试方式码
     */
    @Schema(description = "考试方式码", example = "01")
    @TableField("KSFSM")
    private String examMethodCode;

    /**
     * 补考成绩
     */
    @Schema(description = "补考成绩", example = "75", minimum = "0", maximum = "100")
    @TableField("BKCJ")
    private String makeupScore;

    /**
     * 重修成绩
     */
    @Schema(description = "重修成绩", example = "80", minimum = "0", maximum = "100")
    @TableField("CXCJ")
    private String retakeScore;

    /**
     * 最终成绩
     */
    @Schema(description = "最终成绩", example = "89", minimum = "0", maximum = "100")
    @TableField("ZZCJ")
    private String finalGrade;

    /**
     * 任课教师工号
     */
    @Schema(description = "任课教师工号", example = "T001")
    @TableField("RKJSGH")
    private String teacherJobNumber;

    /**
     * 任课教师姓名
     */
    @Schema(description = "任课教师姓名", example = "李教授")
    @TableField("RKJSXM")
    private String teacherName;

    /**
     * 开课院系编码
     */
    @Schema(description = "开课院系编码", example = "MATH001")
    @TableField("KKYXBM")
    private String offeringDepartmentCode;

    /**
     * 修读院系编码
     */
    @Schema(description = "修读院系编码", example = "CS001")
    @TableField("XDYXBM")
    private String studyDepartmentCode;

    /**
     * 专业编码
     */
    @Schema(description = "专业编码", example = "080901")
    @TableField("ZYBM")
    private String majorCode;

    /**
     * 班级编码
     */
    @Schema(description = "班级编码", example = "CS2023-01")
    @TableField("BJBM")
    private String classCode;

    /**
     * 年级
     */
    @Schema(description = "年级", example = "2023")
    @TableField("NJ")
    private String grade;

    /**
     * 学年
     */
    @Schema(description = "学年", example = "2023-2024")
    @TableField("XN")
    private String academicYear;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳", example = "2023-10-01 10:00:00", format = "date-time")
    @TableField("TTIMESTAMP")
    private String timestamp;

    /**
     * 学号SM4加密
     */
    @Schema(description = "学号SM4加密", hidden = true)
    @TableField("XHSM4")
    private String studentIdSm4;
}
