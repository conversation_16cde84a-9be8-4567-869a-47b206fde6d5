package com.zkdm.iai.domain.entity.student;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：本科生全程绩点信息 (T_GXXS_BKSQCJDXX)
 * 所属部门: 教务处
 * <AUTHOR>
 */
@Schema(description = "本科生全程绩点信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_GXXS_BKSQCJDXX")
public class UndergraduateGpaInfo {

    /**
     * 唯一标识 (主键)
     */
    @Schema(description = "唯一标识", example = "1001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableId(value = "WYBS", type = IdType.INPUT)
    private String id;

    /**
     * 学号
     */
    @Schema(description = "学号", example = "202310001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("XH")
    private String studentId;

    /**
     * GPA计算方式
     */
    @Schema(description = "GPA计算方式", example = "标准4.0制")
    @TableField("GPAJSFS")
    private String gpaCalculationMethod;

    /**
     * GPA
     */
    @Schema(description = "GPA", example = "3.85", minimum = "0", maximum = "4")
    @TableField("GPA")
    private String gpa;

    /**
     * 总学分
     */
    @Schema(description = "总学分", example = "120.5", minimum = "0")
    @TableField("ZXF")
    private String totalCredits;

    /**
     * 是否参与排名
     */
    @Schema(description = "是否参与排名", example = "1", allowableValues = {"0", "1"})
    @TableField("SFCYPM")
    private String participateInRanking;

    /**
     * 更新起始时间
     */
    @Schema(description = "更新起始时间", example = "2023-09-01 00:00:00")
    @TableField("GXRQSJ")
    private String updateStartTime;

    /**
     * 加权平均分
     */
    @Schema(description = "加权平均分", example = "85.6", minimum = "0", maximum = "100")
    @TableField("JQPJF")
    private String weightedAverageScore;

    /**
     * 算术平均分
     */
    @Schema(description = "算术平均分", example = "84.2", minimum = "0", maximum = "100")
    @TableField("SSPJF")
    private String arithmeticAverageScore;

    /**
     * 通过总学分
     */
    @Schema(description = "通过总学分", example = "115.0", minimum = "0")
    @TableField("TGZXF")
    private String passedTotalCredits;

    /**
     * 未通过总学分
     */
    @Schema(description = "未通过总学分", example = "5.5", minimum = "0")
    @TableField("WTCZXF")
    private String failedTotalCredits;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳", example = "2023-10-01 10:00:00", format = "date-time")
    @TableField("TTIMESTAMP")
    private String timestamp;

}
