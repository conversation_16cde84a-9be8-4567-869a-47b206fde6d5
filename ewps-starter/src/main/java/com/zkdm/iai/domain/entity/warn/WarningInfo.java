package com.zkdm.iai.domain.entity.warn;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：学生预警信息表 (T_GXYS_YJXX)
 * 所属部门: 学生工作处/系统生成
 * <AUTHOR>
 */
@Schema(description = "学生预警信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_GXYS_YJXX") // @Table -> @TableName
public class WarningInfo {

    /**
     * 唯一标识 (主键)
     */
    @Schema(description = "唯一标识", example = "W001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableId(value = "WYBS", type = IdType.INPUT) // @Id 和 @Column -> @TableId, type=INPUT 表示手动赋值
    private String id;

    /**
     * 预警所属的学生学号 (外键)。
     * 对应数据库中的 XH 字段。
     * 在 JPA 中，这曾是一个 @ManyToOne 关联的 UndergraduateBasicInfo 对象。
     * 在 MyBatis-Plus 中，我们直接映射外键字段。如需获取完整的学生信息，
     * 需要另行查询或在 Mapper 中进行多表关联查询。
     */
    @Schema(description = "学生学号", example = "202310001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("XH") // @ManyToOne 和 @JoinColumn -> @TableField
    private String studentNo;

    /**
     * 预警类型码 (核心字段)
     */
    @Schema(description = "预警类型码,**预警类型说明**:\n" +
            "- `01`: 学业预警\n" +
            "- `02`: 行为预警\n" +
            "- `03`: 心理预警\n" +
            "- `04`: 经济预警\n" +
            "- `05`: 就业预警\n" +
            "- `06`: 安全预警\n" +
            "- `07`: 健康预警\n" +
            "- `08`: 违纪预警\n" +
            "- `09`: 其他预警", example = "01",
            allowableValues = {"01", "02", "03", "04", "05", "06", "07", "08", "09"})
    @TableField("YJLX_M")
    private String warningTypeCode;

    /**
     * 预警等级码 (1-低, 2-中, 3-高)
     */
    @Schema(description = "预警等级码", example = "3",
            allowableValues = {"1", "2", "3", "4"})
    @TableField("YJDJ_M")
    private String warningLevelCode;

    /**
     * 预警状态码 (01-待处理, 02-处理中, 03-已解决, 04-已忽略)
     */
    @Schema(description = "预警状态码", example = "01",
            allowableValues = {"01", "02", "03", "04"})
    @TableField("YJZT_M")
    private String warningStatusCode;

    /**
     * 预警描述 (核心字段)
     */
    @Schema(description = "预警描述", example = "该学生本学期多门课程成绩不及格，需要重点关注",
            requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("YJMS")
    private String warningDescription;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", example = "2023-10-01 10:00:00", format = "date-time")
    @TableField("CJSJ")
    private String creationTime;

    /**
     * 处理人学号/工号
     */
    @Schema(description = "处理人学号/工号", example = "T001")
    @TableField("CLRXH")
    private String handlerId;

    /**
     * 处理时间
     */
    @Schema(description = "处理时间", example = "2023-10-02 14:30:00", format = "date-time")
    @TableField("CLSJ")
    private String handlingTime;

    /**
     * 备注/处理结果
     */
    @Schema(description = "备注/处理结果", example = "已联系学生家长，制定学习计划")
    @TableField("BZ")
    private String remarks;

    /**
     * 数据来源 (触发预警的源表)
     */
    @Schema(description = "数据来源", example = "T_GXJX_XSXYXX",
            allowableValues = {"T_GXJX_XSXYXX", "T_GXXS_BKSQCJDXX", "T_GXYKT_JYXX", "T_GXTS_TSGZJTXXX"})
    @TableField("SJLY")
    private String dataSource;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳", example = "2023-10-01 10:00:00", format = "date-time")
    @TableField("TTIMESTAMP")
    private String timestamp;
}