package com.zkdm.iai.domain.entity.ecard;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：一卡通交易信息 (T_GXYKT_JYXX)
 * 所属部门: 网络信息中心
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_GXYKT_JYXX")
public class EcardTransaction {

    /**
     * 唯一标识 (主键)
     */
    @TableId(value = "WYBS", type = IdType.INPUT)
    private String id;

    /**
     * 人员GID (关联到人员信息表的唯一标识)
     */
    @TableField("RYGID")
    private String personGid;

    /**
     * 学工号 (关联到人员信息表的学工号)
     */
    @TableField("XGH")
    private String userNumber;

    /**
     * 持卡人姓名 (关联到人员信息表的姓名)
     */
    @TableField("CKRXM")
    private String cardHolderName;

    /**
     * 交易子项编码 (关联代码表: DM_GXYKT_JYZXBM)
     */
    @TableField("JYZXBM")
    private String transactionSubtypeCode;

    /**
     * 交易卡号 (关联到卡片信息表的卡号)
     */
    @TableField("JYKBM")
    private String transactionCardNumber;

    /**
     * 卡片物理卡号 (关联到卡片信息表的物理卡号)
     */
    @TableField("KPLKH")
    private String cardPhysicalNumber;

    /**
     * 钱包号 (关联到钱包信息表的钱包号)
     */
    @TableField("QBH")
    private String walletNumber;

    /**
     * 终端ID号 (关联到终端信息表的终端ID)
     */
    @TableField("ZHDH")
    private String terminalId;

    /**
     * 交易地点
     */
    @TableField("JYDD")
    private String transactionLocation;

    /**
     * 交易时间
     */
    @TableField("JYSJ")
    private String transactionTime;

    /**
     * 终端交易流水号 (关联到终端交易流水表的终端交易流水号)
     */
    @TableField("ZDJYLSH")
    private String terminalTransactionSerial;

    /**
     * 交易冲正序号 (关联到交易冲正表的交易冲正序号)
     */
    @TableField("JYCXXH")
    private String transactionReversalSerial;

    /**
     * 入账时间
     */
    @TableField("RZSJ")
    private String accountingTime;

    /**
     * 交易前金额
     */
    @TableField("JYQJE")
    private String amountBeforeTransaction;

    /**
     * 交易后金额
     */
    @TableField("JYHJE")
    private String amountAfterTransaction;

    /**
     * 交易金额
     */
    @TableField("JYJE")
    private String transactionAmount;

    /**
     * 交易代码 (关联代码表: DM_GXYKT_JYDM)
     */
    @TableField("JYDM")
    private String transactionCode;

    /**
     * 交易名称 (关联代码表: DM_GXYKT_JYDM)
     */
    @TableField("JYMC")
    private String transactionName;

    /**
     * 结算时间
     */
    @TableField("JSSJ")
    private String settlementTime;

    /**
     * 终端授权号 (关联到终端交易流水表的终端授权号)
     */
    @TableField("ZDSQBH")
    private String terminalAuthorizationNumber;

    /**
     * 商户组代码 (关联代码表: DM_GXYKT_SHZDM)
     */
    @TableField("SHZDM")
    private String merchantGroupCode;

    /**
     * 管理费
     */
    @TableField("GLF")
    private String managementFee;

    /**
     * 折扣费
     */
    @TableField("ZKF")
    private String discountFee;

    /**
     * 时间戳
     */
    @TableField("TTIMESTAMP")
    private String timestamp;
}