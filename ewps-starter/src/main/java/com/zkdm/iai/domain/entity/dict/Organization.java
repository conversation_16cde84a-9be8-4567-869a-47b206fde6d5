package com.zkdm.iai.domain.entity.dict;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：组织机构码 (DM_XB_ZZJGM)
 * <AUTHOR>
 */
@Schema(description = "组织机构信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("DM_XB_ZZJGM")
public class Organization {

    /**
     * 代码 (组织机构码)
     */
    @Schema(description = "组织机构码", example = "CS001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableId("ZZJGM")
    private String organizationCode;

    /**
     * 代码名称 (组织机构名称)
     */
    @Schema(description = "组织机构名称", example = "计算机科学与技术学院", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("ZZJGMC")
    private String organizationName;

    /**
     * 上级单位编码
     */
    @Schema(description = "上级单位编码", example = "USTC001")
    @TableField("SJDWBM")
    private String parentCode;

    /**
     * 单位类型
     */
    @Schema(description = "单位类型", example = "学院", allowableValues = {"学校", "学院", "系", "部门"})
    @TableField("DWLX")
    private String type;

    /**
     * 单位级别
     */
    @Schema(description = "单位级别", example = "2", allowableValues = {"1", "2", "3", "4"})
    @TableField("DWJB")
    private String level;

    /**
     * 是否启用 (1:是, 0:否)
     */
    @Schema(description = "是否启用", example = "true")
    @TableField("SFQY")
    private Boolean enabled;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "计算机相关专业")
    @TableField("BZ")
    private String remarks;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳", example = "2023-10-01 10:00:00", format = "date-time")
    @TableField("SJCS")
    private String timestamp;
}
