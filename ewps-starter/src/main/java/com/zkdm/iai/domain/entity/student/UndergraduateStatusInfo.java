package com.zkdm.iai.domain.entity.student;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：本科生学籍基本信息 (T_GXXS_BKSXJJBXX)
 * 所属部门: 教务处
 * <AUTHOR>
 */
@Schema(description = "本科生学籍基本信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_GXXS_BKSXJJBXX")
public class UndergraduateStatusInfo {

    /**
     * 唯一标识 (主键)
     */
    @Schema(description = "唯一标识", example = "1001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableId(value = "WYBS", type = IdType.INPUT)
    private String id;

    /**
     * 人员GID (关联到人员信息表的唯一标识)
     */
    @Schema(description = "人员GID", example = "G001")
    @TableField("GJD")
    private String gjd;

    /**
     * 学号 (关联到本科生基本信息表的学号)
     */
    @Schema(description = "学号", example = "202310001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("XH")
    private String studentId;

    /**
     * 行政区系编码 (关联代码表: DM_GXXS_XZQYXXM)
     */
    @Schema(description = "行政区系编码", example = "001")
    @TableField("XZQYXXM")
    private String administrativeDepartmentCode;

    /**
     * 修读院系编码 (关联代码表: 组织机构码 ( DM_XB_ZZJGM ))
     */
    @Schema(description = "修读院系编码", example = "CS001")
    @TableField("XDYXBM")
    private String academicDepartmentCode;

    /**
     * 专业编码 (关联代码表: DM_XB_BKSZYM)
     */
    @Schema(description = "专业编码", example = "080901")
    @TableField("ZYBM")
    private String majorCode;

    /**
     * 专业班编码 (关联代码表: DM_GXXS_ZYBBM)
     */
    @Schema(description = "专业班编码", example = "CS2023-01")
    @TableField("ZYBBM")
    private String majorClassCode;

    /**
     * 行政班编码 (关联代码表:组织机构码 ( DM_XB_ZZJGM ))
     */
    @Schema(description = "行政班编码", example = "CS2023-A1")
    @TableField("XZBBM")
    private String administrativeClassCode;

    /**
     * 入学年月
     */
    @Schema(description = "入学年月", example = "2023-09", format = "date")
    @TableField("RXNY")
    private String enrollmentDate;

    /**
     * 年级
     */
    @Schema(description = "年级", example = "2023")
    @TableField("NJ")
    private String grade;

    /**
     * 学制
     */
    @Schema(description = "学制", example = "4", allowableValues = {"3", "4", "5"})
    @TableField("XZ")
    private String academicSystem;

    /**
     * 结对方式码
     */
    @Schema(description = "结对方式码", example = "1")
    @TableField("JDFSM")
    private String pairingMethodCode;

    /**
     * 学生类别码
     */
    @Schema(description = "学生类别码", example = "01")
    @TableField("XSLBM")
    private String studentCategoryCode;

    /**
     * 预计毕业日期
     */
    @Schema(description = "预计毕业日期", example = "2027-06-30", format = "date")
    @TableField("YJBYRQ")
    private String expectedGraduationDate;

    /**
     * 实际毕业日期
     */
    @Schema(description = "实际毕业日期", example = "2027-06-30", format = "date")
    @TableField("SJBYRQ")
    private String actualGraduationDate;

    /**
     * 是否在校
     */
    @Schema(description = "是否在校", example = "1", allowableValues = {"0", "1"})
    @TableField("SFZX")
    private String isAtSchool;

    /**
     * 是否在籍
     */
    @Schema(description = "是否在籍", example = "1", allowableValues = {"0", "1"})
    @TableField("SFZJ")
    private String hasStudentStatus;

    /**
     * 学籍状态码
     */
    @Schema(description = "学籍状态码", example = "01")
    @TableField("XJZTM")
    private String studentStatusCode;

    /**
     * 当前状态
     */
    @Schema(description = "当前状态", example = "在读")
    @TableField("DQZT")
    private String currentStatus;

    /**
     * 学习形式码
     */
    @Schema(description = "学习形式码", example = "1")
    @TableField("XXXSM")
    private String studyFormCode;

    /**
     * 培养层次码
     */
    @Schema(description = "培养层次码", example = "1")
    @TableField("PYCCM")
    private String educationLevelCode;

    /**
     * 培养类型码
     */
    @Schema(description = "培养类型码", example = "1")
    @TableField("PYLXM")
    private String trainingTypeCode;

    /**
     * 招生类别码
     */
    @Schema(description = "招生类别码", example = "1")
    @TableField("ZSLBM")
    private String admissionCategoryCode;

    /**
     * 招生方式码
     */
    @Schema(description = "招生方式码", example = "1")
    @TableField("ZSFSM")
    private String admissionMethodCode;

    /**
     * 入学方式码
     */
    @Schema(description = "入学方式码", example = "1")
    @TableField("RXFSM")
    private String enrollmentMethodCode;

    /**
     * 考生号
     */
    @Schema(description = "考生号", example = "23340100123456")
    @TableField("KSH")
    private String candidateNumber;

    /**
     * 考试类型码
     */
    @Schema(description = "考试类型码", example = "1")
    @TableField("KSLXM")
    private String examTypeCode;

    /**
     * 高考总分
     */
    @Schema(description = "高考总分", example = "650", minimum = "0", maximum = "750")
    @TableField("GKZF")
    private String totalScore;

    /**
     * 录取批次码
     */
    @Schema(description = "录取批次码", example = "1")
    @TableField("LQPCM")
    private String admissionBatchCode;

    /**
     * 录取专业码
     */
    @Schema(description = "录取专业码", example = "080901")
    @TableField("LQZYM")
    private String admissionMajorCode;

    /**
     * 生源地省份码
     */
    @Schema(description = "生源地省份码", example = "340000")
    @TableField("SYDSFM")
    private String sourceProvinceCode;

    /**
     * 生源地城市码
     */
    @Schema(description = "生源地城市码", example = "340100")
    @TableField("SYDCSM")
    private String sourceCityCode;

    /**
     * 毕结业结论
     */
    @Schema(description = "毕结业结论", example = "正常毕业")
    @TableField("BJYJL")
    private String graduationConclusion;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳", example = "2023-10-01 10:00:00", format = "date-time")
    @TableField("TTIMESTAMP")
    private String timestamp;

    /**
     * 姓名
     */
    @Schema(description = "姓名", example = "张三")
    @TableField("XM")
    private String name;

    /**
     * 学号SM4加密
     */
    @Schema(description = "学号SM4加密", hidden = true)
    @TableField("XHSM4")
    private String studentIdSm4;

    /**
     * 培养计划编号
     */
    @Schema(description = "培养计划编号", example = "CS2023-PLAN-001")
    @TableField("PYJHB")
    private String trainingPlanNumber;
}
