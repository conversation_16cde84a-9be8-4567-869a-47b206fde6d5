package com.zkdm.iai.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Schema(description = "已获学分VO")
public class CreditsVO {
    @Schema(description = "当前已获学分", example = "44.5")
    private String currentCredits;
    
    @Schema(description = "毕业要求总学分", example = "150")
    private String totalCredits;

    @Schema(description = "班级排名百分比", example = "班级前20%")
    private String classRankPercentage;
}