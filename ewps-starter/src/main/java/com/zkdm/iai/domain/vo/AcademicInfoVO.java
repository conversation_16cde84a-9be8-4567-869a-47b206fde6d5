package com.zkdm.iai.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "学生详细学业指标VO")
public class AcademicInfoVO {

    @Schema(description = "已获学分信息")
    private CreditsVO creditsInfo;

    @Schema(description = "GPA信息")
    private GpaVO gpaInfo;

    @Schema(description = "出勤率信息")
    private AttendanceVO attendanceInfo;

    @Schema(description = "获奖情况信息")
    private AwardsVO awardsInfo;

    @Schema(description = "图书借阅数量信息")
    private BooksBorrowedVO booksBorrowedInfo;

    @Schema(description = "图书馆出入次数信息")
    private LibraryAccessVO libraryAccessInfo;
}