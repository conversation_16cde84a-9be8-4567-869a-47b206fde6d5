package com.zkdm.iai.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 学生GPA信息DTO（包含基本信息）
 * 
 * <AUTHOR>
 */
@Schema(description = "学生GPA信息（包含基本信息）")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudentGpaInfoDto {

    /**
     * 学号
     */
    @Schema(description = "学号", example = "202310001")
    private String studentId;

    /**
     * 姓名
     */
    @Schema(description = "姓名", example = "张三")
    private String name;

    /**
     * 总学分
     */
    @Schema(description = "总学分", example = "120.5")
    private String totalCredits;

    /**
     * GPA
     */
    @Schema(description = "GPA", example = "3.85")
    private String gpa;

    /**
     * 加权平均分
     */
    @Schema(description = "加权平均分", example = "85.6")
    private String weightedAverageScore;

    /**
     * 算术平均分
     */
    @Schema(description = "算术平均分", example = "84.2")
    private String arithmeticAverageScore;

    /**
     * 通过总学分
     */
    @Schema(description = "通过总学分", example = "115.0")
    private String passedTotalCredits;

    /**
     * 未通过总学分
     */
    @Schema(description = "未通过总学分", example = "5.0")
    private String failedTotalCredits;

    /**
     * GPA计算方式
     */
    @Schema(description = "GPA计算方式", example = "标准4.0制")
    private String gpaCalculationMethod;

    /**
     * 是否参与排名
     */
    @Schema(description = "是否参与排名", example = "1")
    private String participateInRanking;

    /**
     * 更新起始时间
     */
    @Schema(description = "更新起始时间", example = "2023-09-01 00:00:00")
    private String updateStartTime;
}
