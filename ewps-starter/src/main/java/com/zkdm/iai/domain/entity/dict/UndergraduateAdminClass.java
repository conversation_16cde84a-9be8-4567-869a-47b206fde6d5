package com.zkdm.iai.domain.entity.dict;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：本科生行政班代码表 (DM_XB_BKSXZB)
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("DM_XB_BKSXZB")
public class UndergraduateAdminClass {

    /**
     * 代码 (行政班编码)
     */
    @TableId("XZBBM")
    private String administrativeClassCode;

    /**
     * 代码名称 (行政班名称)
     */
    @TableField("XZBBMC")
    private String administrativeClassName;

    /**
     * 年级
     */
    @TableField("NJ")
    private String grade;

    /**
     * 是否启用 (1:是, 0:否)
     */
    @TableField("SFQY")
    private Boolean enabled;

    /**
     * 备注
     */
    @TableField("BZ")
    private String remarks;

    /**
     * 时间戳
     * 注意：数据库中为DATETIME类型，这里映射为String以保持与项目中其他实体的一致性。
     * 如果需要进行日期计算，建议在Service层进行转换。
     */
    @TableField("SJCS")
    private String timestamp;
}