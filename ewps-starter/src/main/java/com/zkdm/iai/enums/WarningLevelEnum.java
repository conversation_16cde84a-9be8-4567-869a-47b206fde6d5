package com.zkdm.iai.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Comparator;

@Getter
@RequiredArgsConstructor
public enum WarningLevelEnum {
    // 按优先级从高到低排序
    HIGH("3", "HIGH"),
    MEDIUM("2", "MEDIUM"),
    LOW("1", "LOW"),
    NONE("0", "NONE"); // 为没有预警的学生定义的虚拟等级

    private final String code;
    private final String description;

    /**
     * 根据数据库代码获取枚举实例
     */
    public static WarningLevelEnum fromCode(String code) {
        return Arrays.stream(values())
                .filter(level -> level.code.equals(code))
                .findFirst()
                .orElse(null); // 或者可以抛出异常
    }
    
    /**
     * 从多个预警等级中获取最高的一个
     */
    public static WarningLevelEnum getHighest(WarningLevelEnum... levels) {
        if (levels == null || levels.length == 0) {
            return NONE;
        }
        // min() 配合 ordinal() 可以找到在 enum 中定义最靠前的（即优先级最高的）
        return Arrays.stream(levels)
                .filter(java.util.Objects::nonNull)
                .min(Comparator.comparing(WarningLevelEnum::ordinal)) 
                .orElse(NONE);
    }
}