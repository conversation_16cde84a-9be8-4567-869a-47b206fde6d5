package com.zkdm.iai.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 预警模块枚举
 * 定义四大预警模块及其对应的预警类型码
 */
@Getter
@RequiredArgsConstructor
public enum WarningModuleEnum {
    
    ACADEMIC("ACADEMIC", "学业预警", "学业表现良好", "存在学业风险"),
    TRACK("TRACK", "轨迹预警", "行为轨迹正常", "高度关注"),
    SOCIAL("SOCIAL", "生活预警", "生活状态良好", "活跃度行为满意"),
    PSYCHOLOGICAL("PSYCHOLOGICAL", "心理预警", "心理状态健康", "暂无风险");

    private final String moduleCode;
    private final String moduleName;
    private final String defaultStatusText; // 无预警时的状态文本
    private final String warningStatusText; // 有预警时的状态文本

    /**
     * 根据预警类型码获取对应的预警模块
     * 这里需要根据实际的预警类型码规则来实现
     * 
     * @param warningTypeCode 预警类型码
     * @return 对应的预警模块，如果找不到则返回null
     */
    public static WarningModuleEnum fromWarningTypeCode(String warningTypeCode) {
        if (warningTypeCode == null || warningTypeCode.isEmpty()) {
            return null;
        }
        
        // 根据预警类型码的规则来判断属于哪个模块
        // 这里假设预警类型码的规则如下（需要根据实际情况调整）：
        // 01-09: 学业预警
        // 10-19: 轨迹预警  
        // 20-29: 生活预警
        // 30-39: 心理预警
        
        try {
            int typeCode = Integer.parseInt(warningTypeCode);
            if (typeCode >= 1 && typeCode <= 9) {
                return ACADEMIC;
            } else if (typeCode >= 10 && typeCode <= 19) {
                return TRACK;
            } else if (typeCode >= 20 && typeCode <= 29) {
                return SOCIAL;
            } else if (typeCode >= 30 && typeCode <= 39) {
                return PSYCHOLOGICAL;
            }
        } catch (NumberFormatException e) {
            // 如果不是数字，可以根据字符串前缀来判断
            String upperCode = warningTypeCode.toUpperCase();
            if (upperCode.startsWith("XY") || upperCode.startsWith("ACADEMIC")) {
                return ACADEMIC;
            } else if (upperCode.startsWith("GJ") || upperCode.startsWith("TRACK")) {
                return TRACK;
            } else if (upperCode.startsWith("SH") || upperCode.startsWith("SOCIAL")) {
                return SOCIAL;
            } else if (upperCode.startsWith("XL") || upperCode.startsWith("PSYCH")) {
                return PSYCHOLOGICAL;
            }
        }
        
        return null;
    }

    /**
     * 根据模块代码获取枚举实例
     */
    public static WarningModuleEnum fromModuleCode(String moduleCode) {
        return Arrays.stream(values())
                .filter(module -> module.moduleCode.equals(moduleCode))
                .findFirst()
                .orElse(null);
    }
}
