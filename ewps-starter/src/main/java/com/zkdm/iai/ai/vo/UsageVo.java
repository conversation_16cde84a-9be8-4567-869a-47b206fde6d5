package com.zkdm.iai.ai.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 使用情况统计VO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "使用情况统计")
public class UsageVo {
    
    @Schema(description = "提示词token数量")
    @JsonProperty("prompt_tokens")
    private Integer promptTokens;
    
    @Schema(description = "提示词单价")
    @JsonProperty("prompt_unit_price")
    private String promptUnitPrice;
    
    @Schema(description = "提示词价格单位")
    @JsonProperty("prompt_price_unit")
    private String promptPriceUnit;
    
    @Schema(description = "提示词价格")
    @JsonProperty("prompt_price")
    private String promptPrice;
    
    @Schema(description = "完成token数量")
    @JsonProperty("completion_tokens")
    private Integer completionTokens;
    
    @Schema(description = "完成单价")
    @JsonProperty("completion_unit_price")
    private String completionUnitPrice;
    
    @Schema(description = "完成价格单位")
    @JsonProperty("completion_price_unit")
    private String completionPriceUnit;
    
    @Schema(description = "完成价格")
    @JsonProperty("completion_price")
    private String completionPrice;
    
    @Schema(description = "总token数量")
    @JsonProperty("total_tokens")
    private Integer totalTokens;
    
    @Schema(description = "总价格")
    @JsonProperty("total_price")
    private String totalPrice;
    
    @Schema(description = "货币单位")
    private String currency;
    
    @Schema(description = "延迟时间")
    private Double latency;
}
