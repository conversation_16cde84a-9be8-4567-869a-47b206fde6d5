package com.zkdm.iai.ai.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 会话列表VO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "会话列表响应")
public class ConversationListVo {
    
    @Schema(description = "限制数量")
    private Integer limit;
    
    @Schema(description = "是否有更多数据")
    @JsonProperty("has_more")
    private Boolean hasMore;
    
    @Schema(description = "会话列表")
    private List<ConversationVo> data;
}
