package com.zkdm.iai.ai.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * AI服务提供商枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AiProviderEnum {
    
    /**
     * 通义千问
     */
    QWEN("qwen", "通义千问", "阿里云通义千问大模型"),
    
    /**
     * 自定义第三方服务
     */
    CUSTOM("custom", "自定义服务", "自定义第三方AI服务");
    
    private final String code;
    private final String name;
    private final String description;
    
    /**
     * 根据代码获取枚举
     */
    public static AiProviderEnum fromCode(String code) {
        for (AiProviderEnum provider : values()) {
            if (provider.getCode().equals(code)) {
                return provider;
            }
        }
        return CUSTOM; // 默认返回自定义服务
    }
}
