package com.zkdm.iai.ai.exception;

import com.zkdm.iai.core.domain.R;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * AI服务异常处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice(basePackages = "com.zkdm.iai.ai")
public class AiExceptionHandler {
    
    /**
     * 处理AI服务异常
     */
    @ExceptionHandler(AiServiceException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<Void> handleAiServiceException(AiServiceException e) {
        log.error("AI服务异常: {}", e.getMessage(), e);
        return R.fail("AI服务异常: " + e.getMessage());
    }
    
    /**
     * 处理Feign调用异常
     */
    @ExceptionHandler(FeignException.class)
    @ResponseStatus(HttpStatus.BAD_GATEWAY)
    public R<Void> handleFeignException(FeignException e) {
        log.error("Feign调用异常: status={}, message={}", e.status(), e.getMessage(), e);
        
        String message;
        switch (e.status()) {
            case 400:
                message = "请求参数错误";
                break;
            case 401:
                message = "认证失败，请检查API密钥";
                break;
            case 403:
                message = "访问被拒绝，权限不足";
                break;
            case 404:
                message = "请求的资源不存在";
                break;
            case 429:
                message = "请求频率过高，请稍后重试";
                break;
            case 500:
                message = "AI服务内部错误";
                break;
            case 502:
                message = "AI服务网关错误";
                break;
            case 503:
                message = "AI服务暂时不可用";
                break;
            case 504:
                message = "AI服务响应超时";
                break;
            default:
                message = "AI服务调用失败";
        }
        
        return R.fail(message + " (状态码: " + e.status() + ")");
    }
    
    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<Void> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常: {}", e.getMessage(), e);
        return R.fail("系统内部错误: " + e.getMessage());
    }
}
