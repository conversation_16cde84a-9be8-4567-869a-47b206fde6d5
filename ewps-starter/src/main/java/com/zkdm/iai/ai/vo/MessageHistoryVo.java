package com.zkdm.iai.ai.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 消息历史VO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "消息历史响应")
public class MessageHistoryVo {
    
    @Schema(description = "限制数量")
    private Integer limit;
    
    @Schema(description = "是否有更多数据")
    @JsonProperty("has_more")
    private Boolean hasMore;
    
    @Schema(description = "消息列表")
    private List<MessageVo> data;
}
