package com.zkdm.iai.ai.client;

import com.zkdm.iai.ai.config.FeignConfig;
import com.zkdm.iai.ai.dto.ChatRequestDto;
import com.zkdm.iai.ai.dto.ConversationDeleteDto;
import com.zkdm.iai.ai.dto.ConversationRenameDto;
import com.zkdm.iai.ai.vo.ChatResponseVo;
import com.zkdm.iai.ai.vo.ConversationListVo;
import com.zkdm.iai.ai.vo.ConversationVo;
import com.zkdm.iai.ai.vo.MessageHistoryVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * AI聊天服务Feign客户端
 * 
 * <AUTHOR>
 */
@FeignClient(
    name = "ai-chat-client",
    url = "${ai.chat.base-url}",
    configuration = FeignConfig.class
)
public interface AiChatClient {
    
    /**
     * 基础聊天对话
     * 
     * @param request 聊天请求参数
     * @return 聊天响应
     */
    @PostMapping("/chat-messages")
    ChatResponseVo chatMessages(@RequestBody ChatRequestDto request);
    
    /**
     * 获取历史记录
     * 
     * @param user 用户标识
     * @param conversationId 会话ID
     * @return 消息历史
     */
    @GetMapping("/messages")
    MessageHistoryVo getMessages(
        @RequestParam(required = false) String user,
        @RequestParam(value = "conversation_id", required = false) String conversationId
    );
    
    /**
     * 获取会话列表
     * 
     * @param user 用户标识
     * @return 会话列表
     */
    @GetMapping("/conversations")
    ConversationListVo getConversations(@RequestParam(required = false) String user);
    
    /**
     * 会话重命名
     * 
     * @param conversationId 会话ID
     * @param request 重命名请求参数
     * @return 更新后的会话信息
     */
    @PostMapping("/conversations/{conversation_id}/name")
    ConversationVo renameConversation(
        @PathVariable("conversation_id") String conversationId,
        @RequestBody ConversationRenameDto request
    );
    
    /**
     * 删除会话
     * 
     * @param conversationId 会话ID
     * @param request 删除请求参数
     * @return 删除结果
     */
    @DeleteMapping("/conversations/{conversation_id}")
    Object deleteConversation(
        @PathVariable("conversation_id") String conversationId,
        @RequestBody ConversationDeleteDto request
    );
}
