package com.zkdm.iai.ai.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 会话状态枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ConversationStatusEnum {
    
    /**
     * 活跃状态
     */
    ACTIVE("active", "活跃"),
    
    /**
     * 已完成
     */
    COMPLETED("completed", "已完成"),
    
    /**
     * 已暂停
     */
    PAUSED("paused", "已暂停"),
    
    /**
     * 已删除
     */
    DELETED("deleted", "已删除");
    
    private final String code;
    private final String description;
    
    /**
     * 根据代码获取枚举
     */
    public static ConversationStatusEnum fromCode(String code) {
        for (ConversationStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return ACTIVE; // 默认返回活跃状态
    }
}
