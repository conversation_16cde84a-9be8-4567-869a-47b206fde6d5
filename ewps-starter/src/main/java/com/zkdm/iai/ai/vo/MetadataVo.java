package com.zkdm.iai.ai.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 元数据VO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "响应元数据")
public class MetadataVo {
    
    @Schema(description = "注释回复")
    @JsonProperty("annotation_reply")
    private Object annotationReply;
    
    @Schema(description = "检索资源列表")
    @JsonProperty("retriever_resources")
    private List<String> retrieverResources;
    
    @Schema(description = "使用情况统计")
    private UsageVo usage;
}
