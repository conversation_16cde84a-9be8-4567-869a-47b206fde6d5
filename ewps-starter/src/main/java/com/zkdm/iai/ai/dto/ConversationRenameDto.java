package com.zkdm.iai.ai.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 会话重命名请求DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "会话重命名请求参数")
public class ConversationRenameDto {
    
    @NotBlank(message = "会话名称不能为空")
    @Schema(description = "新的会话名称", example = "大学辅导员角色扮演", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;
    
    @Schema(description = "是否自动生成标题", example = "false", defaultValue = "false")
    @JsonProperty("auto_generate")
    private Boolean autoGenerate = false;
    
    @Schema(description = "用户标识", example = "ewps-system", defaultValue = "ewps-system")
    private String user = "ewps-system";
}
