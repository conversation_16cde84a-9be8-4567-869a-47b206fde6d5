package com.zkdm.iai.ai.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Feign配置类
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class FeignConfig {
    
    private final AiConfig aiConfig;
    
    /**
     * 请求拦截器，用于添加认证头
     */
    @Bean
    public RequestInterceptor requestInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate template) {
                // 添加Authorization头
                template.header("Authorization", "Bearer " + aiConfig.getApiKey());
                // 添加Content-Type头
                template.header("Content-Type", "application/json");
                
                log.debug("Feign request: {} {}", template.method(), template.url());
            }
        };
    }
}
