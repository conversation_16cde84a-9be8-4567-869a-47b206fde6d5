package com.zkdm.iai.ai.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * 会话VO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "会话信息")
public class ConversationVo {
    
    @Schema(description = "会话ID")
    private String id;
    
    @Schema(description = "会话名称")
    private String name;
    
    @Schema(description = "输入参数")
    private Map<String, Object> inputs;
    
    @Schema(description = "状态")
    private String status;
    
    @Schema(description = "介绍")
    private String introduction;
    
    @Schema(description = "创建时间戳")
    @JsonProperty("created_at")
    private Long createdAt;
    
    @Schema(description = "更新时间戳")
    @JsonProperty("updated_at")
    private Long updatedAt;
}
