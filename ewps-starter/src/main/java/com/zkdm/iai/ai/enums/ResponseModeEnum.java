package com.zkdm.iai.ai.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应模式枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ResponseModeEnum {
    
    /**
     * 阻塞模式 - 等待完整响应
     */
    BLOCKING("blocking", "阻塞模式"),
    
    /**
     * 流式模式 - 实时流式响应
     */
    STREAMING("streaming", "流式模式");
    
    private final String code;
    private final String description;
    
    /**
     * 根据代码获取枚举
     */
    public static ResponseModeEnum fromCode(String code) {
        for (ResponseModeEnum mode : values()) {
            if (mode.getCode().equals(code)) {
                return mode;
            }
        }
        throw new IllegalArgumentException("未知的响应模式: " + code);
    }
}
