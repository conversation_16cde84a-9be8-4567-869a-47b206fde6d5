package com.zkdm.iai.ai.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * WebClient配置类
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class WebClientConfig {
    
    private final AiConfig aiConfig;
    
    @Bean
    public WebClient aiWebClient() {
        // 配置HTTP客户端
        HttpClient httpClient = HttpClient.create()
            .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, aiConfig.getConnectTimeout())
            .responseTimeout(Duration.ofMillis(aiConfig.getReadTimeout()))
            .doOnConnected(conn -> 
                conn.addHandlerLast(new ReadTimeoutHandler(aiConfig.getReadTimeout(), TimeUnit.MILLISECONDS))
                    .addHandlerLast(new WriteTimeoutHandler(aiConfig.getConnectTimeout(), TimeUnit.MILLISECONDS)));
        
        return WebClient.builder()
            .baseUrl(aiConfig.getBaseUrl())
            .clientConnector(new ReactorClientHttpConnector(httpClient))
            .defaultHeader("Authorization", "Bearer " + aiConfig.getApiKey())
            .defaultHeader("Content-Type", "application/json")
            .filter(logRequest())
            .filter(logResponse())
            .build();
    }
    
    /**
     * 请求日志过滤器
     */
    private ExchangeFilterFunction logRequest() {
        return ExchangeFilterFunction.ofRequestProcessor(clientRequest -> {
            log.debug("WebClient Request: {} {}", clientRequest.method(), clientRequest.url());
            clientRequest.headers().forEach((name, values) -> 
                values.forEach(value -> log.debug("Request Header: {}={}", name, value)));
            return Mono.just(clientRequest);
        });
    }
    
    /**
     * 响应日志过滤器
     */
    private ExchangeFilterFunction logResponse() {
        return ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {
            log.debug("WebClient Response: {}", clientResponse.statusCode());
            return Mono.just(clientResponse);
        });
    }
}
