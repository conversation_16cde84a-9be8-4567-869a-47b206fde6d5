package com.zkdm.iai.ai.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 流式响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "流式响应数据")
public class StreamResponseDto {
    
    @Schema(description = "事件类型")
    private String event;
    
    @Schema(description = "任务ID")
    @JsonProperty("task_id")
    private String taskId;
    
    @Schema(description = "消息ID")
    @JsonProperty("message_id")
    private String messageId;
    
    @Schema(description = "会话ID")
    @JsonProperty("conversation_id")
    private String conversationId;
    
    @Schema(description = "AI回答内容片段")
    private String answer;
    
    @Schema(description = "创建时间戳")
    @JsonProperty("created_at")
    private Long createdAt;
    
    @Schema(description = "错误信息")
    private String error;
    
    @Schema(description = "是否结束")
    private Boolean finished = false;
    
    @Schema(description = "完整的元数据（仅在结束时提供）")
    private Object data;
}
