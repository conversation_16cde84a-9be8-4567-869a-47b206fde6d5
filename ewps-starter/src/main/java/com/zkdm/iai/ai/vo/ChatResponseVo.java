package com.zkdm.iai.ai.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 聊天响应VO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "聊天响应结果")
public class ChatResponseVo {
    
    @Schema(description = "事件类型")
    private String event;
    
    @Schema(description = "任务ID")
    @JsonProperty("task_id")
    private String taskId;
    
    @Schema(description = "消息ID")
    private String id;
    
    @Schema(description = "消息ID")
    @JsonProperty("message_id")
    private String messageId;
    
    @Schema(description = "会话ID")
    @JsonProperty("conversation_id")
    private String conversationId;
    
    @Schema(description = "模式")
    private String mode;
    
    @Schema(description = "AI回答内容")
    private String answer;
    
    @Schema(description = "元数据")
    private MetadataVo metadata;
    
    @Schema(description = "创建时间戳")
    @JsonProperty("created_at")
    private Long createdAt;
}
