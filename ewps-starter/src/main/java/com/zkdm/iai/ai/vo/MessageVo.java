package com.zkdm.iai.ai.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 消息VO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "消息信息")
public class MessageVo {
    
    @Schema(description = "消息ID")
    private String id;
    
    @Schema(description = "会话ID")
    @JsonProperty("conversation_id")
    private String conversationId;
    
    @Schema(description = "父消息ID")
    @JsonProperty("parent_message_id")
    private String parentMessageId;
    
    @Schema(description = "输入参数")
    private Map<String, Object> inputs;
    
    @Schema(description = "查询内容")
    private String query;
    
    @Schema(description = "回答内容")
    private String answer;
    
    @Schema(description = "消息文件列表")
    @JsonProperty("message_files")
    private List<String> messageFiles;
    
    @Schema(description = "反馈信息")
    private Object feedback;
    
    @Schema(description = "检索资源列表")
    @JsonProperty("retriever_resources")
    private List<String> retrieverResources;
    
    @Schema(description = "创建时间戳")
    @JsonProperty("created_at")
    private Long createdAt;
    
    @Schema(description = "代理思考过程")
    @JsonProperty("agent_thoughts")
    private List<String> agentThoughts;
    
    @Schema(description = "状态")
    private String status;
    
    @Schema(description = "错误信息")
    private Object error;
}
