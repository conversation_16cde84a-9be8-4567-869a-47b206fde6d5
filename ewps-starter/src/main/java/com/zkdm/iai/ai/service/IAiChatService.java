package com.zkdm.iai.ai.service;

import com.zkdm.iai.ai.dto.ChatRequestDto;
import com.zkdm.iai.ai.dto.ConversationRenameDto;
import com.zkdm.iai.ai.vo.ChatResponseVo;
import com.zkdm.iai.ai.vo.ConversationListVo;
import com.zkdm.iai.ai.vo.ConversationVo;
import com.zkdm.iai.ai.vo.MessageHistoryVo;

/**
 * AI聊天服务接口
 * 
 * <AUTHOR>
 */
public interface IAiChatService {
    
    /**
     * 发送聊天消息
     * 
     * @param request 聊天请求参数
     * @return 聊天响应
     */
    ChatResponseVo sendMessage(ChatRequestDto request);
    
    /**
     * 获取消息历史记录
     * 
     * @param user 用户标识
     * @param conversationId 会话ID
     * @return 消息历史
     */
    MessageHistoryVo getMessageHistory(String user, String conversationId);
    
    /**
     * 获取会话列表
     * 
     * @param user 用户标识
     * @return 会话列表
     */
    ConversationListVo getConversationList(String user);
    
    /**
     * 重命名会话
     * 
     * @param conversationId 会话ID
     * @param request 重命名请求参数
     * @return 更新后的会话信息
     */
    ConversationVo renameConversation(String conversationId, ConversationRenameDto request);
    
    /**
     * 删除会话
     * 
     * @param conversationId 会话ID
     * @param user 用户标识
     * @return 删除结果
     */
    Boolean deleteConversation(String conversationId, String user);
}
