package com.zkdm.iai.ai.service.impl;

import com.zkdm.iai.ai.client.AiChatClient;
import com.zkdm.iai.ai.config.AiConfig;
import com.zkdm.iai.ai.dto.ChatRequestDto;
import com.zkdm.iai.ai.dto.ConversationDeleteDto;
import com.zkdm.iai.ai.dto.ConversationRenameDto;
import com.zkdm.iai.ai.exception.AiServiceException;
import com.zkdm.iai.ai.service.IAiChatService;
import com.zkdm.iai.ai.vo.ChatResponseVo;
import com.zkdm.iai.ai.vo.ConversationListVo;
import com.zkdm.iai.ai.vo.ConversationVo;
import com.zkdm.iai.ai.vo.MessageHistoryVo;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * AI聊天服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiChatServiceImpl implements IAiChatService {
    
    private final AiChatClient aiChatClient;
    private final AiConfig aiConfig;
    
    @Override
    public ChatResponseVo sendMessage(ChatRequestDto request) {
        try {
            // 设置默认用户标识
            if (!StringUtils.hasText(request.getUser())) {
                request.setUser(aiConfig.getDefaultUser());
            }
            
            log.info("发送聊天消息，用户: {}, 会话ID: {}, 查询内容: {}", 
                request.getUser(), request.getConversationId(), request.getQuery());
            
            ChatResponseVo response = aiChatClient.chatMessages(request);
            
            log.info("聊天消息发送成功，会话ID: {}, 消息ID: {}", 
                response.getConversationId(), response.getMessageId());
            
            return response;
        } catch (FeignException e) {
            log.error("发送聊天消息失败: {}", e.getMessage(), e);
            throw new AiServiceException("AI聊天服务调用失败", e);
        }
    }
    
    @Override
    public MessageHistoryVo getMessageHistory(String user, String conversationId) {
        try {
            // 设置默认用户标识
            if (!StringUtils.hasText(user)) {
                user = aiConfig.getDefaultUser();
            }
            
            log.info("获取消息历史，用户: {}, 会话ID: {}", user, conversationId);
            
            MessageHistoryVo history = aiChatClient.getMessages(user, conversationId);
            
            log.info("获取消息历史成功，消息数量: {}", 
                history.getData() != null ? history.getData().size() : 0);
            
            return history;
        } catch (FeignException e) {
            log.error("获取消息历史失败: {}", e.getMessage(), e);
            throw new AiServiceException("获取消息历史失败", e);
        }
    }
    
    @Override
    public ConversationListVo getConversationList(String user) {
        try {
            // 设置默认用户标识
            if (!StringUtils.hasText(user)) {
                user = aiConfig.getDefaultUser();
            }
            
            log.info("获取会话列表，用户: {}", user);
            
            ConversationListVo conversations = aiChatClient.getConversations(user);
            
            log.info("获取会话列表成功，会话数量: {}", 
                conversations.getData() != null ? conversations.getData().size() : 0);
            
            return conversations;
        } catch (FeignException e) {
            log.error("获取会话列表失败: {}", e.getMessage(), e);
            throw new AiServiceException("获取会话列表失败", e);
        }
    }
    
    @Override
    public ConversationVo renameConversation(String conversationId, ConversationRenameDto request) {
        try {
            // 设置默认用户标识
            if (!StringUtils.hasText(request.getUser())) {
                request.setUser(aiConfig.getDefaultUser());
            }
            
            log.info("重命名会话，会话ID: {}, 新名称: {}, 用户: {}", 
                conversationId, request.getName(), request.getUser());
            
            ConversationVo conversation = aiChatClient.renameConversation(conversationId, request);
            
            log.info("会话重命名成功，会话ID: {}, 新名称: {}", 
                conversation.getId(), conversation.getName());
            
            return conversation;
        } catch (FeignException e) {
            log.error("重命名会话失败: {}", e.getMessage(), e);
            throw new AiServiceException("重命名会话失败", e);
        }
    }
    
    @Override
    public Boolean deleteConversation(String conversationId, String user) {
        try {
            ConversationDeleteDto request = new ConversationDeleteDto();
            // 设置默认用户标识
            if (!StringUtils.hasText(user)) {
                user = aiConfig.getDefaultUser();
            }
            request.setUser(user);
            
            log.info("删除会话，会话ID: {}, 用户: {}", conversationId, user);
            
            aiChatClient.deleteConversation(conversationId, request);
            
            log.info("会话删除成功，会话ID: {}", conversationId);
            
            return true;
        } catch (FeignException e) {
            log.error("删除会话失败: {}", e.getMessage(), e);
            throw new AiServiceException("删除会话失败", e);
        }
    }
}
