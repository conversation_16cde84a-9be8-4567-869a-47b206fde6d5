package com.zkdm.iai.ai.controller;

import com.zkdm.iai.ai.dto.ChatRequestDto;
import com.zkdm.iai.ai.service.IAiChatService;
import com.zkdm.iai.ai.service.IStreamChatService;
import com.zkdm.iai.ai.vo.ChatResponseVo;
import com.zkdm.iai.ai.vo.ConversationListVo;
import com.zkdm.iai.core.domain.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.time.Duration;

/**
 * AI服务测试控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "AI服务测试", description = "AI服务功能测试接口")
@RestController
@RequestMapping("/api/ai/test")
@RequiredArgsConstructor
public class AiTestController {

    private final IAiChatService aiChatService;
    private final IStreamChatService streamChatService;
    
    @Operation(summary = "快速测试流式聊天", description = "快速测试AI流式聊天功能")
    @GetMapping(value = "/quick-stream-chat", produces = "text/event-stream")
    public Flux<String> quickStreamChat(
            @RequestParam(defaultValue = "你好，请介绍一下你自己") String message) {
        try {
            ChatRequestDto request = new ChatRequestDto();
            request.setQuery(message);
            request.setResponseMode("streaming");
            request.setUser("test-user");

            return streamChatService.sendStreamMessage(request);
        } catch (Exception e) {
            log.error("快速流式测试失败", e);
            return Flux.just("data: {\"event\":\"error\",\"error\":\"测试失败: " + e.getMessage() + "\"}\n\n");
        }
    }

    @Operation(summary = "快速测试聊天（阻塞式）", description = "快速测试AI聊天功能（阻塞式）")
    @GetMapping("/quick-chat")
    public R<ChatResponseVo> quickChat(
            @RequestParam(defaultValue = "你好，请介绍一下你自己") String message) {
        try {
            ChatRequestDto request = new ChatRequestDto();
            request.setQuery(message);
            request.setResponseMode("blocking");
            request.setUser("test-user");

            ChatResponseVo response = aiChatService.sendMessage(request);
            return R.ok("测试成功", response);
        } catch (Exception e) {
            log.error("快速测试失败", e);
            return R.fail("测试失败: " + e.getMessage());
        }
    }
    
    @Operation(summary = "测试获取会话列表", description = "测试获取会话列表功能")
    @GetMapping("/conversations")
    public R<ConversationListVo> testGetConversations() {
        try {
            ConversationListVo conversations = aiChatService.getConversationList("test-user");
            return R.ok("获取成功", conversations);
        } catch (Exception e) {
            log.error("获取会话列表测试失败", e);
            return R.fail("测试失败: " + e.getMessage());
        }
    }
    
    @Operation(summary = "健康检查", description = "检查AI服务是否正常")
    @GetMapping("/health")
    public R<String> healthCheck() {
        return R.ok("AI服务模块正常运行", "OK");
    }

    @Operation(summary = "测试Flux流式输出", description = "测试基本的Flux流式输出功能")
    @GetMapping(value = "/flux-demo", produces = "text/event-stream")
    public Flux<String> fluxDemo() {
        return Flux.interval(Duration.ofSeconds(1))
            .take(5)
            .map(i -> "data: {\"event\":\"message\",\"answer\":\"这是第" + (i + 1) + "条消息\"}\n\n")
            .concatWith(Flux.just("data: {\"event\":\"workflow_finished\",\"finished\":true}\n\n"));
    }
}
