package com.zkdm.iai.ai.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * AI服务配置类
 * 
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "ai.chat")
public class AiConfig {
    
    /**
     * API基础URL
     */
    private String baseUrl = "http://10.100.157.113:82/v1";
    
    /**
     * API密钥
     */
    private String apiKey = "app-Rb5qu3b8pihdp6t56h0XXEAy";
    
    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout = 30000;
    
    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout = 60000;
    
    /**
     * 默认用户标识
     */
    private String defaultUser = "ewps-system";
}
