package com.zkdm.iai.controller;

import com.zkdm.iai.core.domain.R;
import com.zkdm.iai.domain.vo.AcademicInfoVO;
import com.zkdm.iai.service.IAcademicWarningService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller: 用于处理学业相关的预警信息。
 */
@Tag(name = "学业预警", description = "用于获取学生学业预警信息的API")
@RestController
@RequestMapping("/api/v1/warnings/academic")
@RequiredArgsConstructor
@Slf4j
public class AcademicWarningController {

    private final IAcademicWarningService academicWarningService;

    /**
     * GET /api/v1/warnings/academic/{studentId}
     * 获取指定学生的详细学业指标。
     *
     * @param studentId 学生的唯一ID（学号）。
     * @return 包含学生学业信息的响应实体。
     */
    @Operation(summary = "获取学生的详细学业指标",
               description = "获取指定学生的GPA、学分、获奖情况等多项学业指标。")
    @GetMapping("/{studentId}")
    public R<AcademicInfoVO> getAcademicInfo(
            @Parameter(description = "学生的唯一ID（学号）", required = true, example = "202310001")
            @PathVariable String studentId) {

        try {
            AcademicInfoVO academicInfo = academicWarningService.getAcademicInfo(studentId);
            return R.ok("学业信息获取成功", academicInfo);
        } catch (Exception e) {
            // 添加详细的错误日志
            log.error("获取学生{}的学业信息失败", studentId, e);
            return R.fail("获取学业信息失败: " + e.getMessage());
        }
    }

    /**
     * 测试接口 - 获取模拟学业数据
     *
     * @param studentId 学生的唯一ID（学号）。
     * @return 包含学生学业信息的响应实体。
     */
    @Operation(summary = "测试接口 - 获取模拟学业数据",
               description = "用于测试的接口，返回模拟的学业数据")
    @GetMapping("/test/{studentId}")
    public R<String> testAcademicInfo(
            @Parameter(description = "学生的唯一ID（学号）", required = true, example = "202310001")
            @PathVariable String studentId) {

        try {
            AcademicInfoVO academicInfo = academicWarningService.getAcademicInfo(studentId);

            // 构建简化的响应信息
            StringBuilder result = new StringBuilder();
            result.append("学号: ").append(studentId).append("\n");

            if (academicInfo.getGpaInfo() != null) {
                result.append("GPA: ").append(academicInfo.getGpaInfo().getCurrentGpa()).append("/")
                      .append(academicInfo.getGpaInfo().getMaxGpa()).append("\n");
            }

            if (academicInfo.getCreditsInfo() != null) {
                result.append("学分: ").append(academicInfo.getCreditsInfo().getCurrentCredits()).append("/")
                      .append(academicInfo.getCreditsInfo().getTotalCredits()).append("\n");
                result.append("排名: ").append(academicInfo.getCreditsInfo().getClassRankPercentage()).append("\n");
            }

            return R.ok("测试成功", result.toString());
        } catch (Exception e) {
            log.error("测试获取学生{}的学业信息失败", studentId, e);
            return R.fail("测试失败: " + e.getMessage());
        }
    }
}