package com.zkdm.iai.controller.student;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zkdm.iai.core.domain.R;

import com.zkdm.iai.core.utils.SqlUtil;
import com.zkdm.iai.domain.entity.student.UndergraduateBasicInfo;

import com.zkdm.iai.domain.vo.student.StudentListVo;
import com.zkdm.iai.domain.vo.student.StudentOverviewVo;
import com.zkdm.iai.service.student.IStudentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * StudentController
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025 /7/29
 */
@Tag(name = "学生管理", description = "学生信息查询和管理相关接口")
@RestController
@RequestMapping("/students")
@RequiredArgsConstructor
public class StudentController {

    private final IStudentService studentService;


    /**
     * Gets student list.
     *
     * @param query the query
     * @param page  the page
     * @return the student list
     */
    @Operation(summary = "获取学生列表", description = "支持按学号或姓名搜索，支持分页查询")
    @GetMapping
    public R<IPage<StudentListVo>> getStudentList(
            @Parameter(description = "搜索关键词（匹配学号或姓名）", required = false)
            @RequestParam(required = false) String query,
            @Parameter(description = "分页参数", hidden = true)
            Page<UndergraduateBasicInfo> page) {
        // 对查询参数进行基本的SQL注入过滤
        SqlUtil.filterKeyword(query);
        IPage<StudentListVo> studentListPage = studentService.getStudentList(page, query);
        // 使用项目中定义的 R 类来包装响应
        return R.ok("查询成功", studentListPage);
    }


    /**
     * Gets student overview.
     *
     * @param studentId the student id
     * @return the student overview
     */
    @Operation(summary = "获取学生概览", description = "根据学号获取学生的详细档案信息和预警概览")
    @GetMapping("/{studentId}/overview")
    public R<StudentOverviewVo> getStudentOverview(
            @Parameter(description = "学生学号", required = true, example = "202310001")
            @PathVariable String studentId) {
        try {
            StudentOverviewVo overview = studentService.getStudentOverview(studentId);
            return R.ok("查询成功", overview);
        } catch (IllegalArgumentException e) {
            // 如果服务层抛出学生不存在的异常，则返回失败信息
            return R.fail(e.getMessage());
        }
    }
}