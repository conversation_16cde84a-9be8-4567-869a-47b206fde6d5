package com.zkdm.iai;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import java.net.InetAddress;
import java.net.UnknownHostException;

@Slf4j
@SpringBootApplication
@EnableFeignClients(basePackages = "com.zkdm.iai.ai.client") // 启用Feign客户端
public class EwpsApplication {
    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(EwpsApplication.class);
        app.setApplicationStartup(new BufferingApplicationStartup(2048));

        // 启动并拿到容器
        ConfigurableApplicationContext ctx = app.run(args);

        // 1. 取端口
        Environment env = ctx.getEnvironment();
        String port = env.getProperty("server.port", "8080");
        String path = env.getProperty("server.servlet.context-path", "");
        if (!path.endsWith("/")) path += "/";

        // 2. 打印地址
        String host;
        try {
            host = InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            host = "localhost";
        }
        log.info("SpringBoot启动成功, Swagger地址: http://{}:{}{}{}", host, port, path, "swagger-ui/index.html");
    }
}
