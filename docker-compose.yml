version: '3.8'

services:
  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    container_name: ewps-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: student_warning_system
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./数据库初始化脚本.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - ewps-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: ewps-redis
    command: redis-server --requirepass redis_ztPDxc --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ewps-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # 后端应用服务
  ewps-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ewps-backend
    ports:
      - "8080:8080"
    environment:
      # Spring Boot配置
      - SPRING_PROFILES_ACTIVE=docker
      # JVM配置
      - JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0
    volumes:
      # 挂载日志目录
      - ./logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ewps-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

# 网络配置
networks:
  ewps-network:
    driver: bridge
    name: ewps-network

# 数据卷配置
volumes:
  mysql_data:
    name: ewps-mysql-data
  redis_data:
    name: ewps-redis-data
