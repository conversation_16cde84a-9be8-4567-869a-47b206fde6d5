<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zkdm.iai</groupId>
    <artifactId>ewps-backend</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>ewps-backend</name>
    <description>ewps-backend</description>

    <modules>
        <module>ewps-common</module>
        <module>ewps-module</module>
        <module>ewps-starter</module>
    </modules>

    <properties>
        <revision>1.0.0</revision>
        <java.version>17</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <spring-boot.version>3.4.4</spring-boot.version>
        <hutool.version>5.8.39</hutool.version>
        <lombok.version>1.18.36</lombok.version>
        <springdoc.version>2.8.5</springdoc.version>
        <mybatis-plus.version>3.5.11</mybatis-plus.version>
        <p6spy.version>3.9.1</p6spy.version>
        <redis.version>3.3.2</redis.version>
        <jackson.version>2.18.3</jackson.version>
        <fastjson2.version>2.0.53</fastjson2.version>
        <satoken.version>1.43.0</satoken.version>
        <redisson.version>3.45.1</redisson.version>
        <lock4j.version>2.2.7</lock4j.version>
        <mapstruct-plus.version>1.4.6</mapstruct-plus.version>
        <mapstruct-plus.lombok.version>0.2.0</mapstruct-plus.lombok.version>
        <x-file.version>2.1.0</x-file.version>
        <spring-cloud.version>2024.0.0</spring-cloud.version>
        <therapi-javadoc.version>0.15.0</therapi-javadoc.version>

        <!-- 插件版本 -->
        <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
        <maven-war-plugin.version>3.2.2</maven-war-plugin.version>
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <flatten-maven-plugin.version>1.3.0</flatten-maven-plugin.version>

        <!-- 打包默认跳过测试 -->
        <skipTests>true</skipTests>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- hutool 的依赖配置-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- Spring Cloud 的依赖配置-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- sql性能分析插件 -->
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.linpeilie</groupId>
                <artifactId>mapstruct-plus-spring-boot-starter</artifactId>
                <version>${mapstruct-plus.version}</version>
            </dependency>

            <!--redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
            </dependency>

            <!--文档-->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.therapi</groupId>
                <artifactId>therapi-runtime-javadoc</artifactId>
                <version>${therapi-javadoc.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                            <version>${spring-boot.version}</version>
                        </path>
                        <path>
                            <groupId>io.github.linpeilie</groupId>
                            <artifactId>mapstruct-plus-processor</artifactId>
                            <version>${mapstruct-plus.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${mapstruct-plus.lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <!--关于为什么要开启这个接口-->
                    <!-- 启用 -parameters 编译器标志 -->
                    <compilerArgument>-parameters</compilerArgument>
                </configuration>
            </plugin>
            <plugin>
                <!-- 插件的 GroupId，标识插件所属的组织 -->
                <groupId>org.codehaus.mojo</groupId>
                <!-- 插件的 ArtifactId，标识插件的名称 -->
                <artifactId>flatten-maven-plugin</artifactId>
                <!-- 插件的版本号，这里通过变量引用上面定义的版本号 -->
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <!-- 配置扁平化后的 POM 文件的名称 -->
                    <flattenedPomFilename>pom-xml-flattened</flattenedPomFilename>
                    <!-- 是否更新原始 POM 文件 -->
                    <updatePomFile>true</updatePomFile>
                    <!-- 指定扁平化模式，resolveCiFriendliesOnly 表示仅解析 CI 友好的占位符 -->
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <!-- 定义插件的执行配置 -->
                    <execution>
                        <!-- 执行的唯一标识 -->
                        <id>flatten</id>
                        <!-- 指定插件绑定的生命周期阶段 -->
                        <phase>process-resources</phase>
                        <!-- 定义执行的目标 -->
                        <goals>
                            <!-- 执行扁平化操作 -->
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <!-- 定义清理阶段的执行配置 -->
                    <execution>
                        <!-- 清理阶段的唯一标识 -->
                        <id>flatten.clean</id>
                        <!-- 指定清理阶段绑定的生命周期阶段 -->
                        <phase>clean</phase>
                        <!-- 定义清理阶段执行的目标 -->
                        <goals>
                            <!-- 执行清理操作 -->
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>
