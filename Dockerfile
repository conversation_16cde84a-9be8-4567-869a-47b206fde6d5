# 多阶段构建 Dockerfile for USTC Early Warning Perception System Backend

# 第一阶段：构建阶段
FROM maven:3-eclipse-temurin-17 AS builder

# 设置工作目录
WORKDIR /app

# [修改] 先复制 Maven 配置文件
COPY settings.xml .

# [修改] 接着复制所有的 pom.xml 文件，这一步是为了在源代码不变的情况下利用缓存
# 这一步不是必须的，但可以作为一种优化尝试
COPY pom.xml .
COPY ewps-common/pom.xml ewps-common/
COPY ewps-module/pom.xml ewps-module/
COPY ewps-starter/pom.xml ewps-starter/

# [关键修改] 先下载一次依赖，如果失败也没关系，主要是为了尽可能利用缓存
# 如果这一步因为项目结构不完整而失败，会被下一步覆盖
RUN mvn -s settings.xml dependency:go-offline -B || true

# [关键修改] 现在，复制所有的源代码
# 这将确保 Maven 拥有完整的项目结构来解析依赖
COPY . .

# [最终构建] 在完整的项目源码上，再次执行打包，这次包含了下载依赖和编译
# 使用 -s settings.xml 来确保使用国内镜像源
RUN mvn -s settings.xml clean package -DskipTests -B


# 第二阶段：运行阶段
# ... (后续内容保持不变) ...
FROM openjdk:17-jdk-slim

# 设置维护者信息
LABEL maintainer="USTC EWPS Team <<EMAIL>>"
LABEL description="USTC Early Warning Perception System Backend"
LABEL version="1.0.0"

# 安装必要的工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户（安全最佳实践）
RUN groupadd -r ewps && useradd -r -g ewps ewps

# 设置工作目录
WORKDIR /app

# 创建日志目录
RUN mkdir -p /app/logs && chown -R ewps:ewps /app

# 从构建阶段复制JAR文件
# 注意这里的路径可能需要根据你的打包结果调整
COPY --from=builder /app/ewps-starter/target/ewps-starter.jar app.jar

# 更改文件所有者
RUN chown ewps:ewps app.jar

# 切换到非root用户
USER ewps

# 暴露端口
EXPOSE 8081

# 设置环境变量
ENV SPRING_PROFILES_ACTIVE=docker
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0"

# 健康检查
# 修正：健康检查的端口应与 EXPOSE 和应用程序实际监听的端口一致
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8081/actuator/health || exit 1

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]