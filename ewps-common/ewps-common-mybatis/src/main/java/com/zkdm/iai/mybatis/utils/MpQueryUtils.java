package com.zkdm.iai.mybatis.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Predicate;

/**
 * MyBatis-Plus 查询工具类
 * 基于流式API构建复杂查询条件
 * 简化单表CRUD
 * <AUTHOR>
 * @since JDK17
 */
public final class MpQueryUtils {
    
    private MpQueryUtils() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
    
    // ==================== 查询构建器 ====================
    
    /**
     * 创建Lambda查询构建器
     */
    public static <T> QueryBuilder<T> lambdaQuery() {
        return new QueryBuilder<>(new LambdaQueryWrapper<>());
    }
    
    /**
     * 创建Lambda查询构建器（指定实体类）
     */
    public static <T> QueryBuilder<T> lambdaQuery(Class<T> entityClass) {
        return new QueryBuilder<>(new LambdaQueryWrapper<>(entityClass));
    }
    
    /**
     * 创建普通查询构建器
     */
    public static <T> QueryBuilder<T> query() {
        return new QueryBuilder<>(new QueryWrapper<>());
    }
    
    // ==================== 更新构建器 ====================
    
    /**
     * 创建Lambda更新构建器
     */
    public static <T> UpdateBuilder<T> lambdaUpdate() {
        return new UpdateBuilder<>(new LambdaUpdateWrapper<>());
    }
    
    /**
     * 创建Lambda更新构建器（指定实体类）
     */
    public static <T> UpdateBuilder<T> lambdaUpdate(Class<T> entityClass) {
        return new UpdateBuilder<>(new LambdaUpdateWrapper<>(entityClass));
    }
    
    /**
     * 创建普通更新构建器
     */
    public static <T> UpdateBuilder<T> update() {
        return new UpdateBuilder<>(new UpdateWrapper<>());
    }
    
    // ==================== 分页构建器 ====================
    
    /**
     * 创建分页对象
     */
    public static <T> Page<T> page(long current, long size) {
        return new Page<>(current, size);
    }
    
    /**
     * 创建分页对象（带排序）
     */
    public static <T> Page<T> page(long current, long size, boolean isAsc, String... columns) {
        return new Page<T>(current, size).addOrder(
            isAsc ? com.baomidou.mybatisplus.core.metadata.OrderItem.asc(Arrays.toString(columns))
                  : com.baomidou.mybatisplus.core.metadata.OrderItem.desc(Arrays.toString(columns))
        );
    }
    
    // ==================== 查询构建器实现 ====================
    
    /**
     * 查询构建器 - 提供流式API构建查询条件
     */
    public static final class QueryBuilder<T> {
        private final LambdaQueryWrapper<T> lambdaWrapper;
        private final QueryWrapper<T> queryWrapper;
        private final boolean isLambda;
        
        @SuppressWarnings("unchecked")
        private QueryBuilder(Object wrapper) {
            if (wrapper instanceof LambdaQueryWrapper) {
                this.lambdaWrapper = (LambdaQueryWrapper<T>) wrapper;
                this.queryWrapper = null;
                this.isLambda = true;
            } else {
                this.lambdaWrapper = null;
                this.queryWrapper = (QueryWrapper<T>) wrapper;
                this.isLambda = false;
            }
        }
        
        // ==================== 条件判断方法 ====================
        
        /**
         * 条件为真时执行
         */
        public QueryBuilder<T> when(boolean condition, Consumer<QueryBuilder<T>> consumer) {
            if (condition) {
                consumer.accept(this);
            }
            return this;
        }
        
        /**
         * 值非空时执行
         */
        public QueryBuilder<T> whenNotNull(Object value, Consumer<QueryBuilder<T>> consumer) {
            return when(Objects.nonNull(value), consumer);
        }
        
        /**
         * 字符串非空时执行
         */
        public QueryBuilder<T> whenNotBlank(String value, Consumer<QueryBuilder<T>> consumer) {
            return when(Objects.nonNull(value) && !value.trim().isEmpty(), consumer);
        }
        
        /**
         * 集合非空时执行
         */
        public QueryBuilder<T> whenNotEmpty(Collection<?> collection, Consumer<QueryBuilder<T>> consumer) {
            return when(Objects.nonNull(collection) && !collection.isEmpty(), consumer);
        }
        
        // ==================== Lambda条件方法 ====================
        
        /**
         * 等于条件
         */
        public QueryBuilder<T> eq(SFunction<T, ?> column, Object value) {
            return eq(true, column, value);
        }
        
        public QueryBuilder<T> eq(boolean condition, SFunction<T, ?> column, Object value) {
            if (isLambda) {
                lambdaWrapper.eq(condition, column, value);
            }
            return this;
        }
        
        /**
         * 不等于条件
         */
        public QueryBuilder<T> ne(SFunction<T, ?> column, Object value) {
            return ne(true, column, value);
        }
        
        public QueryBuilder<T> ne(boolean condition, SFunction<T, ?> column, Object value) {
            if (isLambda) {
                lambdaWrapper.ne(condition, column, value);
            }
            return this;
        }
        
        /**
         * 大于条件
         */
        public QueryBuilder<T> gt(SFunction<T, ?> column, Object value) {
            return gt(true, column, value);
        }
        
        public QueryBuilder<T> gt(boolean condition, SFunction<T, ?> column, Object value) {
            if (isLambda) {
                lambdaWrapper.gt(condition, column, value);
            }
            return this;
        }
        
        /**
         * 大于等于条件
         */
        public QueryBuilder<T> ge(SFunction<T, ?> column, Object value) {
            return ge(true, column, value);
        }
        
        public QueryBuilder<T> ge(boolean condition, SFunction<T, ?> column, Object value) {
            if (isLambda) {
                lambdaWrapper.ge(condition, column, value);
            }
            return this;
        }
        
        /**
         * 小于条件
         */
        public QueryBuilder<T> lt(SFunction<T, ?> column, Object value) {
            return lt(true, column, value);
        }
        
        public QueryBuilder<T> lt(boolean condition, SFunction<T, ?> column, Object value) {
            if (isLambda) {
                lambdaWrapper.lt(condition, column, value);
            }
            return this;
        }
        
        /**
         * 小于等于条件
         */
        public QueryBuilder<T> le(SFunction<T, ?> column, Object value) {
            return le(true, column, value);
        }
        
        public QueryBuilder<T> le(boolean condition, SFunction<T, ?> column, Object value) {
            if (isLambda) {
                lambdaWrapper.le(condition, column, value);
            }
            return this;
        }
        
        /**
         * 模糊查询
         */
        public QueryBuilder<T> like(SFunction<T, ?> column, Object value) {
            return like(true, column, value);
        }
        
        public QueryBuilder<T> like(boolean condition, SFunction<T, ?> column, Object value) {
            if (isLambda) {
                lambdaWrapper.like(condition, column, value);
            }
            return this;
        }
        
        /**
         * 左模糊查询
         */
        public QueryBuilder<T> likeLeft(SFunction<T, ?> column, Object value) {
            return likeLeft(true, column, value);
        }
        
        public QueryBuilder<T> likeLeft(boolean condition, SFunction<T, ?> column, Object value) {
            if (isLambda) {
                lambdaWrapper.likeLeft(condition, column, value);
            }
            return this;
        }
        
        /**
         * 右模糊查询
         */
        public QueryBuilder<T> likeRight(SFunction<T, ?> column, Object value) {
            return likeRight(true, column, value);
        }
        
        public QueryBuilder<T> likeRight(boolean condition, SFunction<T, ?> column, Object value) {
            if (isLambda) {
                lambdaWrapper.likeRight(condition, column, value);
            }
            return this;
        }
        
        /**
         * IN条件
         */
        public QueryBuilder<T> in(SFunction<T, ?> column, Collection<?> values) {
            return in(true, column, values);
        }
        
        public QueryBuilder<T> in(boolean condition, SFunction<T, ?> column, Collection<?> values) {
            if (isLambda) {
                lambdaWrapper.in(condition, column, values);
            }
            return this;
        }
        
        /**
         * NOT IN条件
         */
        public QueryBuilder<T> notIn(SFunction<T, ?> column, Collection<?> values) {
            return notIn(true, column, values);
        }
        
        public QueryBuilder<T> notIn(boolean condition, SFunction<T, ?> column, Collection<?> values) {
            if (isLambda) {
                lambdaWrapper.notIn(condition, column, values);
            }
            return this;
        }
        
        /**
         * BETWEEN条件
         */
        public QueryBuilder<T> between(SFunction<T, ?> column, Object val1, Object val2) {
            return between(true, column, val1, val2);
        }
        
        public QueryBuilder<T> between(boolean condition, SFunction<T, ?> column, Object val1, Object val2) {
            if (isLambda) {
                lambdaWrapper.between(condition, column, val1, val2);
            }
            return this;
        }
        
        /**
         * IS NULL条件
         */
        public QueryBuilder<T> isNull(SFunction<T, ?> column) {
            return isNull(true, column);
        }
        
        public QueryBuilder<T> isNull(boolean condition, SFunction<T, ?> column) {
            if (isLambda) {
                lambdaWrapper.isNull(condition, column);
            }
            return this;
        }
        
        /**
         * IS NOT NULL条件
         */
        public QueryBuilder<T> isNotNull(SFunction<T, ?> column) {
            return isNotNull(true, column);
        }
        
        public QueryBuilder<T> isNotNull(boolean condition, SFunction<T, ?> column) {
            if (isLambda) {
                lambdaWrapper.isNotNull(condition, column);
            }
            return this;
        }
        
        // ==================== 排序方法 ====================
        
        /**
         * 升序排序
         */
        @SafeVarargs
        public final QueryBuilder<T> orderByAsc(SFunction<T, ?>... columns) {
            if (isLambda) {
                lambdaWrapper.orderByAsc(Arrays.asList(columns));
            }
            return this;
        }
        
        /**
         * 降序排序
         */
        @SafeVarargs
        public final QueryBuilder<T> orderByDesc(SFunction<T, ?>... columns) {
            if (isLambda) {
                lambdaWrapper.orderByDesc(Arrays.asList(columns));
            }
            return this;
        }
        
        // ==================== 字段选择 ====================
        
        /**
         * 选择字段
         */
        @SafeVarargs
        public final QueryBuilder<T> select(SFunction<T, ?>... columns) {
            if (isLambda) {
                lambdaWrapper.select(columns);
            }
            return this;
        }
        
        // ==================== 时间范围便捷方法 ====================
        
        /**
         * 时间范围查询（开始时间 <= 字段 <= 结束时间）
         */
        public QueryBuilder<T> timeBetween(SFunction<T, ?> column, LocalDateTime startTime, LocalDateTime endTime) {
            if (Objects.nonNull(startTime)) {
                ge(column, startTime);
            }
            if (Objects.nonNull(endTime)) {
                le(column, endTime);
            }
            return this;
        }
        
        // ==================== 构建方法 ====================
        
        /**
         * 构建LambdaQueryWrapper
         */
        public LambdaQueryWrapper<T> build() {
            return isLambda ? lambdaWrapper : null;
        }
        
        /**
         * 构建QueryWrapper
         */
        public QueryWrapper<T> buildQuery() {
            return isLambda ? null : queryWrapper;
        }
    }
    
    // ==================== 更新构建器实现 ====================
    
    /**
     * 更新构建器 - 提供流式API构建更新条件
     */
    public static final class UpdateBuilder<T> {
        private final LambdaUpdateWrapper<T> lambdaWrapper;
        private final UpdateWrapper<T> updateWrapper;
        private final boolean isLambda;
        
        @SuppressWarnings("unchecked")
        private UpdateBuilder(Object wrapper) {
            if (wrapper instanceof LambdaUpdateWrapper) {
                this.lambdaWrapper = (LambdaUpdateWrapper<T>) wrapper;
                this.updateWrapper = null;
                this.isLambda = true;
            } else {
                this.lambdaWrapper = null;
                this.updateWrapper = (UpdateWrapper<T>) wrapper;
                this.isLambda = false;
            }
        }
        
        /**
         * 条件为真时执行
         */
        public UpdateBuilder<T> when(boolean condition, Consumer<UpdateBuilder<T>> consumer) {
            if (condition) {
                consumer.accept(this);
            }
            return this;
        }
        
        /**
         * 设置字段值
         */
        public UpdateBuilder<T> set(SFunction<T, ?> column, Object value) {
            return set(true, column, value);
        }
        
        public UpdateBuilder<T> set(boolean condition, SFunction<T, ?> column, Object value) {
            if (isLambda) {
                lambdaWrapper.set(condition, column, value);
            }
            return this;
        }
        
        /**
         * 字段值非空时设置
         */
        public UpdateBuilder<T> setIfNotNull(SFunction<T, ?> column, Object value) {
            return set(Objects.nonNull(value), column, value);
        }
        
        /**
         * 字符串非空时设置
         */
        public UpdateBuilder<T> setIfNotBlank(SFunction<T, ?> column, String value) {
            return set(Objects.nonNull(value) && !value.trim().isEmpty(), column, value);
        }
        
        // 继承查询条件方法（为了支持WHERE条件）
        public UpdateBuilder<T> eq(SFunction<T, ?> column, Object value) {
            if (isLambda) {
                lambdaWrapper.eq(column, value);
            }
            return this;
        }
        
        public UpdateBuilder<T> ne(SFunction<T, ?> column, Object value) {
            if (isLambda) {
                lambdaWrapper.ne(column, value);
            }
            return this;
        }
        
        public UpdateBuilder<T> in(SFunction<T, ?> column, Collection<?> values) {
            if (isLambda) {
                lambdaWrapper.in(column, values);
            }
            return this;
        }
        
        /**
         * 构建LambdaUpdateWrapper
         */
        public LambdaUpdateWrapper<T> build() {
            return isLambda ? lambdaWrapper : null;
        }
        
        /**
         * 构建UpdateWrapper
         */
        public UpdateWrapper<T> buildUpdate() {
            return isLambda ? null : updateWrapper;
        }
    }
    
    // ==================== 静态工具方法 ====================
    
    /**
     * 条件断言 - 简化条件判断
     */
    public static final class Conditions {
        
        /**
         * 检查值是否非空
         */
        public static boolean notNull(Object value) {
            return Objects.nonNull(value);
        }
        
        /**
         * 检查字符串是否非空白
         */
        public static boolean notBlank(String value) {
            return Objects.nonNull(value) && !value.trim().isEmpty();
        }
        
        /**
         * 检查集合是否非空
         */
        public static boolean notEmpty(Collection<?> collection) {
            return Objects.nonNull(collection) && !collection.isEmpty();
        }
        
        /**
         * 自定义条件断言
         */
        public static <T> boolean test(T value, Predicate<T> predicate) {
            return Objects.nonNull(value) && predicate.test(value);
        }
    }
}