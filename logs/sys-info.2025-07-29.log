2025-07-29 09:13:04 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-29 09:13:04 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 16356 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-29 09:13:04 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-29 09:13:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 09:13:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 09:13:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
2025-07-29 09:13:06 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=22ccbb54-07d1-3d72-b98f-762e2cf7f8b8
2025-07-29 09:13:07 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 09:13:07 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2648 ms
2025-07-29 09:13:08 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-29 09:13:09 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-29 09:13:09 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-29 09:13:09 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-29 09:13:10 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-29 09:13:11 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-29 09:13:11 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-29 09:13:11 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-29 09:13:11 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 09:13:11 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8081 (http) with context path '/'
2025-07-29 09:13:11 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 6.829 seconds (process running for 8.037)
2025-07-29 09:13:11 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://*************:8081/swagger-ui/index.html
2025-07-29 09:13:11 [RMI TCP Connection(1)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-29 09:13:11 [RMI TCP Connection(4)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 09:13:11 [RMI TCP Connection(4)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-29 09:13:11 [RMI TCP Connection(4)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-29 09:13:16 [RMI TCP Connection(1)-*************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@24c8ab8d
2025-07-29 09:13:16 [RMI TCP Connection(1)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-29 09:15:40 [XNIO-1 task-4] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 877 ms
2025-07-29 09:18:22 [XNIO-1 task-4] INFO  c.z.i.a.s.impl.AiChatServiceImpl - 发送聊天消息，用户: ewps-system, 会话ID: string, 查询内容: 学生情绪压抑该如何解决？
2025-07-29 09:38:57 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-29 09:38:57 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-29 09:38:57 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-29 09:38:57 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 09:38:57 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-29 09:38:57 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-29 17:52:46 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-29 17:52:46 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 20564 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-29 17:52:46 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-29 17:52:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 17:52:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 17:52:47 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 44 ms. Found 0 Redis repository interfaces.
2025-07-29 17:52:48 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=22ccbb54-07d1-3d72-b98f-762e2cf7f8b8
2025-07-29 17:52:49 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-29 17:52:49 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2882 ms
2025-07-29 17:52:50 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-29 17:52:54 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-29 17:53:06 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-29 17:53:06 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-29 17:53:08 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-29 17:53:08 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-29 17:53:08 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-29 17:53:08 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-29 17:53:08 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-29 17:53:08 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8081 (http) with context path '/'
2025-07-29 17:53:08 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 22.679 seconds (process running for 24.07)
2025-07-29 17:53:08 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://*************:8081/swagger-ui/index.html
2025-07-29 17:53:08 [RMI TCP Connection(5)-*************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 17:53:08 [RMI TCP Connection(5)-*************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-29 17:53:08 [RMI TCP Connection(5)-*************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-29 17:53:08 [RMI TCP Connection(4)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-29 17:53:12 [XNIO-1 task-4] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 925 ms
2025-07-29 17:53:14 [RMI TCP Connection(4)-*************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@2e8b5348
2025-07-29 17:53:14 [RMI TCP Connection(4)-*************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-29 17:56:42 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-29 17:56:42 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-29 17:56:42 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-29 17:56:42 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-29 17:56:42 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-29 17:56:42 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
