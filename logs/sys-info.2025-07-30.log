2025-07-30 15:12:59 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 15:12:59 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 504 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-30 15:12:59 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-30 15:13:00 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 15:13:00 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 15:13:00 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 39 ms. Found 0 Redis repository interfaces.
2025-07-30 15:13:01 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=05392083-818a-3fcf-948d-05036ee1e940
2025-07-30 15:13:02 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-30 15:13:02 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2810 ms
2025-07-30 15:13:03 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-30 15:13:51 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 15:13:51 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 36624 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-30 15:13:51 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-30 15:13:52 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 15:13:52 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 15:13:52 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-07-30 15:13:53 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=05392083-818a-3fcf-948d-05036ee1e940
2025-07-30 15:13:54 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-30 15:13:54 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2470 ms
2025-07-30 15:13:55 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-30 15:14:40 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-07-30 15:14:58 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-30 15:14:58 [main] INFO  com.zkdm.iai.EwpsApplication - Starting EwpsApplication using Java 17.0.15 with PID 16064 (C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back\ewps-starter\target\classes started by lqj in C:\Users\<USER>\Desktop\ustc-earlywarningperceptionsystem-back)
2025-07-30 15:14:58 [main] INFO  com.zkdm.iai.EwpsApplication - The following 1 profile is active: "dev"
2025-07-30 15:14:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-30 15:14:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-30 15:14:59 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 43 ms. Found 0 Redis repository interfaces.
2025-07-30 15:15:00 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=05392083-818a-3fcf-948d-05036ee1e940
2025-07-30 15:15:01 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-30 15:15:01 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2821 ms
2025-07-30 15:15:02 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-30 15:15:02 [redisson-netty-1-6] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for **************/**************:6377
2025-07-30 15:15:03 [redisson-netty-1-19] INFO  o.r.connection.ConnectionsHolder - 24 connections initialized for **************/**************:6377
2025-07-30 15:15:03 [main] INFO  c.z.iai.redis.config.RedissonConfig - Redisson 已启动
2025-07-30 15:15:04 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-30 15:15:04 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-30 15:15:04 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-30 15:15:04 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-30 15:15:04 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-30 15:15:04 [main] INFO  o.s.b.w.e.undertow.UndertowWebServer - Undertow started on port 8081 (http) with context path '/'
2025-07-30 15:15:04 [main] INFO  com.zkdm.iai.EwpsApplication - Started EwpsApplication in 7.386 seconds (process running for 8.734)
2025-07-30 15:15:04 [main] INFO  com.zkdm.iai.EwpsApplication - SpringBoot启动成功, Swagger地址: http://***************:8081/swagger-ui/index.html
2025-07-30 15:15:05 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-30 15:15:05 [RMI TCP Connection(5)-***************] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 15:15:05 [RMI TCP Connection(5)-***************] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-30 15:15:05 [RMI TCP Connection(5)-***************] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-30 15:15:09 [XNIO-1 task-6] INFO  o.s.api.AbstractOpenApiResource - Init duration for springdoc-openapi is: 1238 ms
2025-07-30 15:15:10 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@36484d74
2025-07-30 15:15:10 [RMI TCP Connection(2)-***************] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-30 16:09:29 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-07-30 16:09:29 [SpringApplicationShutdownHook] INFO  o.s.b.w.e.undertow.UndertowWebServer - Graceful shutdown complete
2025-07-30 16:09:29 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-07-30 16:09:29 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-30 16:09:29 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-30 16:09:29 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
